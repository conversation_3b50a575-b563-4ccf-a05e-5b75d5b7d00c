# 持仓系统V14版本 - 腾讯云托管部署说明

## ✅ 准备状态
你的V14版本已经准备完成，可以直接部署到腾讯云托管！

## 📋 V14版本特性
- 显示逻辑优化版
- 减半后显示逻辑改进
- 持仓数量直接点击编辑
- 完整的股票管理功能
- 多策略卖出信号
- 扫雷功能集成
- 年内最低价缓存

## 🚀 腾讯云托管部署配置

### 基本配置
- **部署方式**: 通过本地代码部署
- **项目目录**: `c:\Users\<USER>\Desktop\持仓系统 - 修改中`
- **Dockerfile名称**: `Dockerfile`
- **端口**: `80`

### 服务配置
- **服务名称**: `portfolio-system-v14`
- **部署类型**: 容器型服务
- **内存**: 512MB
- **CPU**: 0.25核

## 📁 已准备的文件

### 核心文件
- ✅ `app.py` - V14主应用（从持仓系统_v14.py复制）
- ✅ `main.py` - 腾讯云托管入口文件
- ✅ `Dockerfile` - V14专用容器配置
- ✅ `requirements.txt` - Python依赖

### 依赖模块
- ✅ `A股交易时间监测_简化版.py`
- ✅ `yearly_low_cache_reader.py`
- ✅ `tdx_scan_module.py`

### 数据文件
- ✅ `stock_data_cache.json`
- ✅ `imported_stock_list.json`
- ✅ `strategy_config.json`
- ✅ `auto_update_config.json`
- ✅ `reduction_monitoring_config.json`

## 🎯 部署步骤

### 1. 登录腾讯云控制台
- 访问: https://console.cloud.tencent.com/
- 进入"云托管"服务

### 2. 创建服务
- 点击"新建服务"
- 选择"通过本地代码部署"

### 3. 配置信息

#### 代码包类型
- 选择: **压缩包**

#### 服务相关
- **服务名称**: `portfolio-system-v14`
- **部署类型**: **容器型服务**

#### 容器配置
- **端口**: `80`
- **Dockerfile名称**: `Dockerfile`

#### 高级设置（可选）
- **环境变量**:
  ```
  FLASK_ENV=production
  PYTHONPATH=/app
  PORT=80
  ```

### 4. 选择代码目录
- 选择你的项目根目录：
  ```
  c:\Users\<USER>\Desktop\持仓系统 - 修改中
  ```

### 5. 部署
- 点击"部署"按钮
- 等待部署完成（约3-5分钟）

## 🔧 V14版本功能

### 核心功能
- 📊 股票持仓管理
- 📈 实时股价更新
- 🎯 多策略卖出信号
- ⏰ 交易时间监测
- 💰 年内最低价对比
- 🎮 扫雷宝评分
- 📱 企业微信提醒

### V14特色功能
- ✨ 减半后显示逻辑优化
- 🖱️ 持仓数量直接编辑
- 📋 智能状态管理
- 🔄 自动数据刷新

## ⚠️ 重要提醒

1. **这是你的原版V14** - 保持所有原有功能
2. **端口已设置为80** - 符合腾讯云托管要求
3. **自动适配云环境** - 无需手动修改代码
4. **数据文件已包含** - 保持现有配置

## 🎉 部署成功后

部署成功后你将获得：
- 完整的V14版本持仓系统
- 云端访问URL
- 所有原有功能正常运行
- 自动扩缩容能力

## 🔍 故障排除

### 常见问题
1. **部署失败**: 检查目录选择是否正确
2. **启动失败**: 查看部署日志中的错误信息
3. **功能异常**: 检查依赖文件是否完整

### 检查清单
- ✅ 选择正确的项目目录
- ✅ Dockerfile名称为"Dockerfile"
- ✅ 端口设置为80
- ✅ 所有依赖文件都在根目录

## 📞 技术支持

如果遇到问题：
1. 检查腾讯云托管的部署日志
2. 确认所有文件都在项目根目录
3. 验证Dockerfile配置是否正确

现在你可以直接在腾讯云托管界面进行部署了！🚀
