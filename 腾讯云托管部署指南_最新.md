# 持仓系统V14 - 腾讯云托管部署指南

## 📦 部署包信息

- **文件名**: `持仓系统V14_腾讯云托管部署包_20250730_144638.zip`
- **大小**: 72KB
- **包含文件**: 30个
- **平台**: 腾讯云托管

## 🚀 快速部署步骤

### 1. 登录腾讯云控制台
- 访问: https://console.cloud.tencent.com/
- 搜索并进入"云托管"服务

### 2. 创建服务
- 点击"新建服务"
- **服务名称**: `portfolio-system`
- **地域**: 建议选择上海或北京
- **网络**: 选择默认VPC

### 3. 配置部署
- **部署方式**: 选择"代码包部署"
- **上传文件**: 选择生成的ZIP文件
- **运行环境**: Python 3.9

### 4. 服务配置
```
启动命令: python main.py
端口: 5000
CPU: 0.25核
内存: 512MB
```

### 5. 环境变量（可选）
```
FLASK_ENV=production
PYTHONPATH=/app
```

### 6. 部署并访问
- 点击"部署"按钮
- 等待部署完成（约2-5分钟）
- 通过提供的URL访问系统

## 📋 部署包内容

### 核心文件
- `main.py` - 入口文件
- `app.py` - Flask应用主文件
- `requirements.txt` - Python依赖
- `Dockerfile` - 容器配置

### 功能模块
- `modules/` - 核心业务模块
  - `data_fetcher.py` - 数据获取
  - `strategy_manager.py` - 策略管理
  - `sell_signal.py` - 卖出信号
  - `cache_manager.py` - 缓存管理
  - `trading_time_monitor.py` - 交易时间监测

### 配置文件
- `config/strategy_config.json` - 策略配置
- `stock_data_cache.json` - 股票数据缓存
- `imported_stock_list.json` - 导入股票列表

### 前端模板
- `templates/index.html` - 主页面模板

## ⚙️ 系统功能

### 核心功能
- 📊 股票持仓管理
- 📈 实时数据更新
- 🎯 多策略卖出信号
- ⏰ 交易时间监测
- 📱 企业微信提醒

### 技术特性
- 🔄 自动数据刷新
- 💾 智能缓存系统
- 📋 Excel导入导出
- 🎨 响应式界面
- 🔒 数据持久化

## 🔧 故障排除

### 常见问题

1. **部署失败**
   - 检查ZIP文件是否完整
   - 确认Python版本为3.9+
   - 查看部署日志

2. **启动失败**
   - 检查启动命令是否正确
   - 确认端口设置为5000
   - 查看应用日志

3. **数据不显示**
   - 检查网络连接
   - 确认API接口可访问
   - 查看错误日志

### 日志查看
- 在云托管控制台查看"运行日志"
- 关注启动和错误信息

## 📞 技术支持

如遇到问题，请检查：
1. 部署配置是否正确
2. 网络连接是否正常
3. 日志中的错误信息

## 🎉 部署完成

部署成功后，你将获得：
- 一个可访问的Web应用URL
- 完整的股票持仓管理系统
- 实时数据更新功能
- 多策略卖出信号提醒

祝你使用愉快！🎊
