# 🏠 本地部署简易教程

## 🎯 什么是本地部署？

**简单理解：**
- 你的持仓系统在**你的电脑**上运行
- 使用**花生壳**让外网用户能访问你的电脑
- 就像把你的电脑变成一个小型服务器

## 🔄 工作流程图

```
[你的电脑] ←→ [花生壳客户端] ←→ [花生壳服务器] ←→ [用户浏览器]
    ↑                                                      ↑
运行持仓系统                                        访问你的网站
```

## 📋 超简单5步部署

### 第1步：下载花生壳
- 访问：https://hsk.oray.com/download/
- 下载Windows客户端
- 安装并注册账号（需要手机号）

### 第2步：实名认证
- 上传身份证照片
- 等待审核（通常几分钟）
- 获得免费域名（如：abc123.vicp.fun）

### 第3步：配置内网穿透
- 打开花生壳客户端
- 点击"内网穿透"
- 添加映射：
  - 应用名：持仓系统
  - 内网主机：127.0.0.1
  - 内网端口：5000
  - 外网域名：选择你的免费域名

### 第4步：启动持仓系统
```bash
# 在你的项目文件夹打开命令行
python 持仓系统_v14.py
```

### 第5步：测试访问
- 在浏览器输入你的花生壳域名
- 如：http://abc123.vicp.fun
- 应该能看到你的持仓系统

## ✅ 成功标志

如果看到以下内容，说明部署成功：
- ✅ 花生壳显示"在线"状态
- ✅ 本地系统显示"🚀 启动持仓系统..."
- ✅ 外网地址能正常访问

## 🔒 安全加强（可选）

如果想要更安全，可以运行：
```bash
python 安全启动脚本.py
```

这会添加：
- 🔐 访问密码保护
- 🌐 IP地址限制
- ⏰ 访问时间限制

## 💡 常见问题

### Q: 我的电脑关机了，别人还能访问吗？
A: 不能。本地部署需要你的电脑保持开机。

### Q: 会不会很耗电？
A: 不会。持仓系统很轻量，几乎不耗资源。

### Q: 网速会影响访问吗？
A: 会有一些影响，但一般家庭宽带完全够用。

### Q: 安全吗？
A: 非常安全！代码和数据都在你的电脑上，没有第三方能访问。

## 🆚 与云部署对比

| 特性 | 本地部署 | 云部署 |
|------|----------|--------|
| 代码安全 | 🔒 在你电脑上 | ⚠️ 在云服务器上 |
| 运行要求 | 需要开机 | 无需开机 |
| 费用 | 完全免费 | 可能收费 |
| 控制权 | 完全控制 | 受平台限制 |
| 隐私 | 100%私密 | 依赖平台政策 |

## 🎉 总结

**本地部署适合你，因为：**
1. 你担心代码泄露 → 本地部署代码不离开电脑
2. 你是中国用户 → 花生壳访问速度快
3. 你想要免费 → 完全不花钱
4. 你想要控制权 → 一切都在你手中

**开始部署吧！整个过程只需要15分钟！** 🚀
