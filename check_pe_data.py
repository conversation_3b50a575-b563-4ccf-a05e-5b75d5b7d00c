#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查股票数据中的TTM市盈率字段
"""

import json
import os

def check_pe_data():
    """检查TTM市盈率数据"""
    data_file = 'stock_data_cache.json'
    
    if not os.path.exists(data_file):
        print("❌ 未找到股票数据文件")
        return
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        stocks = data.get('stock_data', {})
        if not stocks:
            print("❌ 股票数据为空")
            return
        
        print(f"📊 总股票数量: {len(stocks)}")
        print("\n🔍 前10只股票的TTM市盈率数据:")
        print("-" * 60)
        
        for i, (code, info) in enumerate(stocks.items()):
            if i >= 10:
                break
            
            name = info.get('name', '未知')
            pe_ttm = info.get('pe_ttm', None)
            
            if pe_ttm is None:
                pe_str = "无数据"
            elif pe_ttm == 0:
                pe_str = "0"
            elif pe_ttm < 0:
                pe_str = f"{pe_ttm} (负值)"
            else:
                pe_str = f"{pe_ttm}"
            
            print(f"{code}: {name} - TTM市盈率: {pe_str}")
        
        # 统计TTM市盈率数据情况
        total_count = len(stocks)
        has_pe_data = 0
        positive_pe = 0
        negative_pe = 0
        zero_pe = 0
        none_pe = 0
        
        for info in stocks.values():
            pe_ttm = info.get('pe_ttm', None)
            
            if pe_ttm is None:
                none_pe += 1
            elif pe_ttm == 0:
                zero_pe += 1
                has_pe_data += 1
            elif pe_ttm < 0:
                negative_pe += 1
                has_pe_data += 1
            else:
                positive_pe += 1
                has_pe_data += 1
        
        print("\n📈 TTM市盈率数据统计:")
        print("-" * 60)
        print(f"总股票数量: {total_count}")
        print(f"有TTM市盈率数据: {has_pe_data} ({has_pe_data/total_count*100:.1f}%)")
        print(f"  - 正值: {positive_pe} ({positive_pe/total_count*100:.1f}%)")
        print(f"  - 负值: {negative_pe} ({negative_pe/total_count*100:.1f}%)")
        print(f"  - 零值: {zero_pe} ({zero_pe/total_count*100:.1f}%)")
        print(f"无TTM市盈率数据: {none_pe} ({none_pe/total_count*100:.1f}%)")
        
        # 检查是否有其他可能的市盈率字段
        print("\n🔍 检查其他可能的市盈率字段:")
        print("-" * 60)
        
        sample_stock = next(iter(stocks.values()))
        pe_fields = [key for key in sample_stock.keys() if 'pe' in key.lower() or 'ratio' in key.lower()]
        
        if pe_fields:
            print("发现的可能市盈率相关字段:")
            for field in pe_fields:
                print(f"  - {field}")
        else:
            print("未发现其他市盈率相关字段")
        
        # 显示一个完整的股票数据示例
        print(f"\n📋 完整股票数据示例 ({next(iter(stocks.keys()))}):")
        print("-" * 60)
        sample_data = next(iter(stocks.values()))
        for key, value in sample_data.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")

if __name__ == "__main__":
    check_pe_data()
