#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建腾讯云托管部署包
将所有必需文件打包成ZIP文件，适用于腾讯云托管平台
"""

import os
import zipfile
import json
import shutil
from datetime import datetime

def create_deployment_package():
    """创建腾讯云托管部署包"""

    # 部署包文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f'持仓系统V14_腾讯云托管部署包_{timestamp}.zip'

    # 使用clean_deployment目录作为基础
    base_dir = 'clean_deployment'

    # 需要打包的文件列表（从clean_deployment目录）
    files_to_package = [
        # 核心应用文件
        'app.py',
        'requirements.txt',
        'Dockerfile',
        'gunicorn_config.py',
        'start.sh',
        'stop.sh',

        # 模块目录
        'modules/',

        # 模板目录
        'templates/',

        # 配置目录
        'config/',

        # 其他必要目录
        'cache/',
        'logs/',
        'uploads/',
    ]

    # 额外需要的根目录文件
    root_files = [
        'stock_data_cache.json',
        'imported_stock_list.json',
        'strategy_config.json',
        'auto_update_config.json',
        'reduction_monitoring_config.json',
    ]

    print(f"📦 开始创建腾讯云托管部署包: {zip_filename}")
    print("=" * 60)

    # 检查clean_deployment目录是否存在
    if not os.path.exists('clean_deployment'):
        print("❌ clean_deployment目录不存在，无法创建部署包")
        return None

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:

        # 打包clean_deployment目录中的文件
        print("📁 打包clean_deployment目录:")
        for root, dirs, files in os.walk('clean_deployment'):
            for file in files:
                file_path = os.path.join(root, file)
                # 计算在ZIP中的相对路径（去掉clean_deployment前缀）
                arcname = os.path.relpath(file_path, 'clean_deployment').replace('\\', '/')
                zipf.write(file_path, arcname)
                file_size = os.path.getsize(file_path)
                print(f"  ✅ {arcname} ({file_size:,} bytes)")

        # 打包根目录的额外文件
        print("\n📁 打包根目录额外文件:")
        for file in root_files:
            if os.path.exists(file):
                zipf.write(file)
                file_size = os.path.getsize(file)
                print(f"  ✅ {file} ({file_size:,} bytes)")
            else:
                print(f"  ⚠️ {file} - 文件不存在（可选）")

        # 创建腾讯云托管专用的启动文件
        print("\n📁 创建腾讯云托管配置文件:")

        # 创建main.py作为入口文件
        main_py_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云托管入口文件
"""
from app import app

if __name__ == "__main__":
    # 腾讯云托管会自动设置端口
    import os
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=False)
'''
        zipf.writestr('main.py', main_py_content)
        print(f"  ✅ main.py (入口文件)")

        # 创建package.json（如果需要）
        package_json = {
            "name": "portfolio-system",
            "version": "1.0.0",
            "description": "持仓系统",
            "main": "main.py",
            "scripts": {
                "start": "python main.py"
            }
        }
        zipf.writestr('package.json', json.dumps(package_json, ensure_ascii=False, indent=2))
        print(f"  ✅ package.json")

        # 创建部署说明文件
        deploy_info = {
            "package_name": zip_filename,
            "created_time": datetime.now().isoformat(),
            "version": "持仓系统V14",
            "platform": "腾讯云托管",
            "files_count": len(zipf.namelist()),
            "deployment_steps": [
                "1. 登录腾讯云控制台",
                "2. 进入云托管服务",
                "3. 创建新服务或选择现有服务",
                "4. 选择代码包部署方式",
                "5. 上传此ZIP文件",
                "6. 设置启动命令: python main.py",
                "7. 配置端口: 5000",
                "8. 部署并访问服务"
            ],
            "requirements": {
                "python_version": "3.9+",
                "memory": "512MB",
                "cpu": "0.25核",
                "port": 5000,
                "startup_command": "python main.py"
            },
            "environment_variables": {
                "FLASK_ENV": "production",
                "PYTHONPATH": "/app"
            }
        }

        # 添加部署信息文件
        zipf.writestr('部署说明.json', json.dumps(deploy_info, ensure_ascii=False, indent=2))
        print(f"  ✅ 部署说明.json")

        # 创建README.md
        readme_content = '''# 持仓系统V14 - 腾讯云托管部署包

## 部署步骤

1. **登录腾讯云控制台**
   - 访问 https://console.cloud.tencent.com/
   - 进入云托管服务

2. **创建服务**
   - 选择"新建服务"
   - 服务名称: portfolio-system
   - 地域: 根据需要选择

3. **上传代码包**
   - 选择"代码包部署"
   - 上传此ZIP文件
   - 等待上传完成

4. **配置服务**
   - 启动命令: `python main.py`
   - 端口: 5000
   - 内存: 512MB
   - CPU: 0.25核

5. **环境变量**（可选）
   ```
   FLASK_ENV=production
   PYTHONPATH=/app
   ```

6. **部署并访问**
   - 点击部署
   - 等待部署完成
   - 通过提供的URL访问系统

## 系统功能

- 股票持仓管理
- 多策略卖出信号
- 实时数据更新
- 交易时间监测
- 企业微信提醒

## 技术栈

- Python 3.9+
- Flask Web框架
- pandas数据处理
- 东方财富API
- 通达信扫雷功能

## 注意事项

1. 首次部署可能需要几分钟时间
2. 确保网络连接正常
3. 如有问题请检查日志
'''
        zipf.writestr('README.md', readme_content)
        print(f"  ✅ README.md")

    # 显示打包结果
    zip_size = os.path.getsize(zip_filename)
    print("\n" + "=" * 60)
    print(f"🎉 腾讯云托管部署包创建完成!")
    print(f"📦 文件名: {zip_filename}")
    print(f"📊 大小: {zip_size:,} bytes ({zip_size/1024/1024:.2f} MB)")

    # 验证ZIP文件
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_count = len(zipf.namelist())
            print(f"📁 包含文件: {file_count} 个")
            print(f"✅ ZIP文件完整性验证通过")
    except Exception as e:
        print(f"❌ ZIP文件验证失败: {e}")

    print("\n🚀 腾讯云托管部署步骤:")
    print("1. 登录腾讯云控制台 -> 云托管")
    print("2. 创建新服务或选择现有服务")
    print("3. 选择代码包部署，上传ZIP文件")
    print("4. 设置启动命令: python main.py")
    print("5. 设置端口: 5000")
    print("6. 部署并访问")

    return zip_filename

if __name__ == "__main__":
    create_deployment_package()
