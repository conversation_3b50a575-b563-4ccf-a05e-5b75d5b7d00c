# 持仓系统 V14 CloudBase版本

## 概述

这是持仓系统V14的腾讯云CloudBase兼容版本，支持云函数部署，具备完整的股票持仓管理功能。

## 主要特性

### 🌟 核心功能
- **股票数据管理**：实时获取股价、PE、PB、股息率等关键指标
- **智能卖出策略**：多策略自动生成卖出信号
- **持仓分析**：完整的持仓统计和风险评估
- **数据可视化**：饼图展示持仓分布
- **移动端适配**：响应式设计，支持手机访问

### ☁️ CloudBase特性
- **无服务器部署**：基于腾讯云CloudBase云函数
- **自动扩缩容**：根据访问量自动调整资源
- **高可用性**：云平台保障服务稳定性
- **成本优化**：按使用量付费，无闲置成本

## 部署指南

### 前置条件

1. **安装Node.js和npm**
2. **安装CloudBase CLI**
   ```bash
   npm install -g @cloudbase/cli
   ```
3. **登录腾讯云**
   ```bash
   tcb login
   ```

### 部署步骤

#### 方法一：使用部署脚本（推荐）

**Linux/Mac:**
```bash
chmod +x deploy_cloudbase_v14.sh
./deploy_cloudbase_v14.sh
```

**Windows:**
```cmd
deploy_cloudbase_v14.bat
```

#### 方法二：手动部署

1. **准备配置文件**
   ```bash
   cp cloudbaserc_v14.json cloudbaserc.json
   ```

2. **部署云函数**
   ```bash
   tcb functions:deploy portfolio-system-v14
   ```

3. **查看部署状态**
   ```bash
   tcb functions:list
   ```

## 配置说明

### 环境变量

在`cloudbaserc_v14.json`中配置以下环境变量：

- `WECHAT_WEBHOOK_URL`: 企业微信机器人Webhook地址
- `TENCENTCLOUD_RUNENV`: 设置为"SCF"标识云函数环境
- `FLASK_ENV`: 设置为"production"

### 函数配置

- **运行时**: Python 3.9
- **内存**: 1024MB
- **超时**: 120秒
- **并发**: 根据需要调整

## 功能差异

### CloudBase版本特点

✅ **支持的功能**
- 所有核心股票管理功能
- Web界面和API接口
- 数据导入导出
- 实时数据获取
- 企业微信提醒

⚠️ **限制和调整**
- 禁用后台自动更新线程（云函数无状态特性）
- 文件存储使用临时目录
- 数据持久化依赖外部存储或数据库
- 缓存生命周期与函数实例绑定

### 与本地版本的区别

| 特性 | 本地版本 | CloudBase版本 |
|------|----------|---------------|
| 后台更新 | ✅ 支持 | ❌ 禁用 |
| 文件存储 | 本地文件系统 | 临时目录 |
| 数据持久化 | 本地JSON文件 | 内存（会话级别） |
| 自动启动 | 手动启动 | 按需启动 |
| 资源消耗 | 持续占用 | 按使用量 |

## 使用指南

### 访问地址

部署成功后，通过以下地址访问：
```
https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/
```

### 主要操作

1. **上传持仓数据**
   - 支持Excel文件上传
   - 自动解析股票代码和持仓数量

2. **查看股票信息**
   - 实时股价和涨跌幅
   - PE、PB、股息率等指标
   - 卖出信号和建议

3. **数据管理**
   - 清空数据
   - 导出数据
   - 状态备份和恢复

## 监控和维护

### 查看日志
```bash
tcb functions:log portfolio-system-v14
```

### 更新函数
```bash
tcb functions:deploy portfolio-system-v14
```

### 查看监控
```bash
tcb functions:detail portfolio-system-v14
```

## 故障排除

### 常见问题

1. **部署失败**
   - 检查CloudBase CLI是否已登录
   - 确认配置文件格式正确
   - 检查依赖文件是否完整

2. **函数超时**
   - 增加超时时间配置
   - 优化代码性能
   - 检查网络请求是否正常

3. **内存不足**
   - 增加内存配置
   - 优化数据结构
   - 减少同时处理的数据量

### 调试方法

1. **查看函数日志**
   ```bash
   tcb functions:log portfolio-system-v14 --tail
   ```

2. **本地测试**
   ```bash
   python 持仓系统_v14.py
   ```

3. **检查配置**
   ```bash
   tcb functions:config:get portfolio-system-v14
   ```

## 版本历史

- **V14-CloudBase**: 腾讯云CloudBase兼容版本
  - 适配云函数环境
  - 优化资源使用
  - 改进错误处理

## 技术支持

如遇问题，请检查：
1. CloudBase CLI版本是否最新
2. 腾讯云账户权限是否充足
3. 网络连接是否正常
4. 配置文件是否正确

## 许可证

本项目仅供学习和研究使用。
