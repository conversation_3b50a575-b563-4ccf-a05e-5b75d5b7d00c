# 🚀 持仓系统V14 腾讯云上传部署指南

## 📦 部署包信息

**文件名**: `持仓系统V14_腾讯云部署包_20250730_143712.zip`  
**大小**: 11.73 MB  
**包含文件**: 32个  
**版本**: 持仓系统V14完整版

## 🎯 上传部署方式

### 方式一：腾讯云控制台上传（推荐）

1. **登录腾讯云控制台**
   - 访问：https://console.cloud.tencent.com/
   - 登录你的腾讯云账号

2. **进入云函数服务**
   - 搜索"云函数" 或 "SCF"
   - 点击进入云函数控制台

3. **创建函数**
   - 点击"新建"
   - 选择"自定义创建"
   - 运行环境：Python 3.7
   - 函数名称：portfolio-system

4. **上传代码包**
   - 提交方法：选择"本地上传zip包"
   - 点击"上传"，选择 `持仓系统V14_腾讯云部署包_20250730_143712.zip`
   - 执行方法：`portfolio_system_v14.main_handler`

5. **配置函数**
   - 内存：512MB
   - 超时时间：60秒
   - 环境变量：
     ```
     FLASK_ENV = production
     PORT = 8080
     WECHAT_WEBHOOK_URL = 你的企业微信机器人地址
     ```

6. **部署并测试**
   - 点击"完成"
   - 等待部署完成
   - 点击"测试"验证函数

### 方式二：CLI命令行部署

1. **解压部署包**
   ```bash
   # 解压ZIP文件到本地目录
   unzip 持仓系统V14_腾讯云部署包_20250730_143712.zip
   cd 解压目录
   ```

2. **安装腾讯云CLI**
   ```bash
   npm install -g @cloudbase/cli
   ```

3. **登录腾讯云**
   ```bash
   tcb login
   ```

4. **一键部署**
   ```bash
   # Windows
   deploy.bat
   
   # Linux/Mac
   chmod +x deploy_cloudbase.sh
   ./deploy_cloudbase.sh
   ```

## 🌐 访问地址

部署成功后，你的持仓系统将在以下地址可用：

```
https://service-xxx-1234567890.ap-shanghai.apigateway.myqcloud.com/release/portfolio-system
```

或者如果使用CloudBase：
```
https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/
```

## ⚙️ 重要配置

### 环境变量设置
在腾讯云控制台 → 云函数 → 环境配置中设置：

| 变量名 | 值 | 说明 |
|--------|----|----|
| `FLASK_ENV` | `production` | 生产环境模式 |
| `PORT` | `8080` | 服务端口 |
| `WECHAT_WEBHOOK_URL` | `你的机器人地址` | 企业微信推送 |

### 触发器配置
- 触发方式：API网关触发
- 请求方法：ANY
- 发布环境：发布
- 鉴权方法：免鉴权

## 📋 部署包内容清单

### ✅ 核心文件
- `portfolio_system_v14.py` - 主程序（已添加云函数入口）
- `requirements.txt` - Python依赖包
- `cloudbaserc.json` - 云开发配置

### ✅ 功能模块
- `A股交易时间监测_简化版.py` - 交易时间监测
- `yearly_low_cache_reader.py` - 年内最低价缓存
- `tdx_scan_module.py` - 扫雷功能模块

### ✅ 真实数据
- `stock_data_cache.json` - 你的真实股票数据
- `imported_stock_list.json` - 导入的股票列表
- `uploads/` - 历史上传文件

### ✅ 配置文件
- `auto_update_config.json` - 自动更新配置
- `strategy_config.json` - 策略配置
- `reduction_monitoring_config.json` - 减半监控配置

### ✅ 部署工具
- `deploy.bat` / `deploy_cloudbase.sh` - 部署脚本
- `check_deploy.py` - 部署检查工具
- `腾讯云部署说明.md` - 详细说明文档

## 🔧 常见问题

### 1. 上传失败
- 检查ZIP文件大小（应该是11.73MB）
- 确保网络连接稳定
- 尝试重新上传

### 2. 函数超时
- 增加超时时间到120秒
- 增加内存到1024MB

### 3. 依赖包错误
- 检查Python版本（使用3.7）
- 确认requirements.txt完整

### 4. 访问404错误
- 检查API网关配置
- 确认触发器设置正确

## 💰 费用预估

### 免费额度（每月）
- 调用次数：100万次
- 资源使用量：40万GBs
- 外网出流量：1GB

### 预计费用
正常使用情况下，你的持仓系统每月费用预计：
- **0-5元** （在免费额度内）

## 🎉 部署完成后

部署成功后，你将拥有：

✅ **云端持仓系统** - 随时随地访问你的真实持仓数据  
✅ **实时股价更新** - 自动获取最新股价信息  
✅ **智能卖出提醒** - 多种策略自动分析  
✅ **企业微信推送** - 重要信号及时通知  
✅ **扫雷风险评估** - 智能风险分析  
✅ **高可用服务** - 99.95%服务可用性  

现在你可以把ZIP文件上传到腾讯云开始部署了！🚀

---

**注意**: 这是你的真实持仓系统V14，包含所有完整功能，不是演示版本！
