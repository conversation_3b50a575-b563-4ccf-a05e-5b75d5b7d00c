<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudBase股息率获取测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #007bff;
            margin-top: 0;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .result {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .cache-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .cache-table th, .cache-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .cache-table th {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status-valid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌤️ CloudBase股息率获取测试</h1>
            <p>测试腾讯云CloudBase环境下的雪球股息率获取功能</p>
        </div>

        <!-- 单股票测试 -->
        <div class="test-section">
            <h3>📊 单股票股息率测试</h3>
            <div class="input-group">
                <label>股票代码:</label>
                <input type="text" id="singleStockCode" placeholder="例如: 000001" value="000001">
                <button class="btn" onclick="testSingleStock()">获取股息率</button>
            </div>
            <div id="singleStockResult" class="result" style="display: none;"></div>
        </div>

        <!-- 批量测试 -->
        <div class="test-section">
            <h3>📈 批量股票测试</h3>
            <p>测试多只股票的股息率获取性能</p>
            <button class="btn" onclick="testBatchStocks()">批量测试</button>
            <div id="batchTestResult" class="result" style="display: none;"></div>
        </div>

        <!-- 缓存状态 -->
        <div class="test-section">
            <h3>💾 缓存状态监控</h3>
            <button class="btn" onclick="checkCacheStatus()">查看缓存状态</button>
            <button class="btn btn-danger" onclick="clearCache()">清理缓存</button>
            <div id="cacheStatusResult" class="result" style="display: none;"></div>
        </div>

        <!-- 环境信息 -->
        <div class="test-section">
            <h3>🔧 环境信息</h3>
            <button class="btn" onclick="checkEnvironment()">检查环境</button>
            <div id="environmentResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 测试单只股票
        async function testSingleStock() {
            const stockCode = document.getElementById('singleStockCode').value.trim();
            const resultDiv = document.getElementById('singleStockResult');
            
            if (!stockCode) {
                showResult(resultDiv, '请输入股票代码', 'error');
                return;
            }

            showResult(resultDiv, '正在获取股息率...', 'success');

            try {
                const response = await fetch(`/api/test-dividend/${stockCode}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    const result = `股票代码: ${stockCode}
股息率: ${data.dividend_yield}%
获取方式: ${data.source || '雪球'}
耗时: ${data.duration || 'N/A'}ms
缓存状态: ${data.cached ? '命中缓存' : '实时获取'}`;
                    showResult(resultDiv, result, 'success');
                } else {
                    showResult(resultDiv, `获取失败: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }

        // 批量测试
        async function testBatchStocks() {
            const resultDiv = document.getElementById('batchTestResult');
            const testStocks = ['000001', '000002', '600000', '600036', '000858'];
            
            showResult(resultDiv, '开始批量测试...', 'success');

            try {
                const startTime = Date.now();
                const results = [];
                
                for (const stock of testStocks) {
                    const response = await fetch(`/api/test-dividend/${stock}`, {
                        method: 'POST'
                    });
                    const data = await response.json();
                    results.push({
                        stock,
                        success: data.success,
                        dividend_yield: data.dividend_yield || 0,
                        source: data.source || 'unknown',
                        cached: data.cached || false
                    });
                }
                
                const totalTime = Date.now() - startTime;
                const successCount = results.filter(r => r.success).length;
                
                let resultText = `批量测试完成
总耗时: ${totalTime}ms
成功: ${successCount}/${testStocks.length}
平均耗时: ${Math.round(totalTime / testStocks.length)}ms/股

详细结果:
`;
                
                results.forEach(r => {
                    resultText += `${r.stock}: ${r.success ? r.dividend_yield + '%' : '失败'} (${r.source}${r.cached ? ', 缓存' : ''})\n`;
                });
                
                showResult(resultDiv, resultText, 'success');
            } catch (error) {
                showResult(resultDiv, `批量测试失败: ${error.message}`, 'error');
            }
        }

        // 检查缓存状态
        async function checkCacheStatus() {
            const resultDiv = document.getElementById('cacheStatusResult');
            
            try {
                const response = await fetch('/api/dividend-cache-status');
                const data = await response.json();
                
                if (data.success) {
                    const info = data.data;
                    let resultText = `缓存状态信息
环境: ${info.is_cloudbase ? 'CloudBase' : '本地'}
缓存大小: ${info.cache_size}
有效缓存: ${info.summary.valid_cache}
过期缓存: ${info.summary.expired_cache}

缓存详情:
`;
                    
                    if (info.cache_size > 0) {
                        resultText += `股票代码 | 股息率 | 缓存时间 | 状态\n`;
                        resultText += `-------|-------|--------|------\n`;
                        
                        Object.entries(info.cache_stats).forEach(([stock, stats]) => {
                            resultText += `${stock} | ${stats.value}% | ${stats.age_minutes}分钟前 | ${stats.is_valid ? '有效' : '过期'}\n`;
                        });
                    } else {
                        resultText += '暂无缓存数据';
                    }
                    
                    showResult(resultDiv, resultText, 'success');
                } else {
                    showResult(resultDiv, `获取缓存状态失败: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }

        // 清理缓存
        async function clearCache() {
            const resultDiv = document.getElementById('cacheStatusResult');
            
            try {
                const response = await fetch('/api/clear-dividend-cache', {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    showResult(resultDiv, `缓存清理成功: ${data.message}`, 'success');
                } else {
                    showResult(resultDiv, `清理缓存失败: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }

        // 检查环境
        async function checkEnvironment() {
            const resultDiv = document.getElementById('environmentResult');
            
            try {
                const response = await fetch('/api/env');
                const data = await response.json();
                
                if (data.success) {
                    const env = data.environment;
                    const resultText = `环境信息
CloudBase环境: ${env.TENCENTCLOUD_RUNENV === 'SCF' ? '是' : '否'}
Flask环境: ${env.FLASK_ENV}
微信提醒: ${env.WECHAT_WEBHOOK_URL}
端口: ${env.PORT}
是否CloudBase: ${env.is_cloudbase ? '是' : '否'}`;
                    
                    showResult(resultDiv, resultText, 'success');
                } else {
                    showResult(resultDiv, '获取环境信息失败', 'error');
                }
            } catch (error) {
                showResult(resultDiv, `请求失败: ${error.message}`, 'error');
            }
        }

        // 显示结果
        function showResult(element, text, type) {
            element.textContent = text;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 页面加载时自动检查环境
        window.onload = function() {
            checkEnvironment();
        };
    </script>
</body>
</html>
