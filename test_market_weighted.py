#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市值加权指标计算测试脚本
"""

def calculate_market_weighted_metrics(stock_data):
    """
    计算市值加权指标
    
    Args:
        stock_data (dict): 股票数据字典
        
    Returns:
        dict: 包含市值加权指标的字典
    """
    if not stock_data:
        return {
            'total_market_value': 0,
            'stock_count': 0,
            'market_weighted_dividend_yield_ttm': 0,
            'market_weighted_pb_ratio': 0,
            'market_weighted_pe_ttm': 0,
            'valid_dividend_stocks': 0,
            'valid_pb_stocks': 0,
            'valid_pe_stocks': 0
        }

    # 总市值
    total_market_value = sum(stock.get('market_value', 0) for stock in stock_data.values())
    stock_count = len(stock_data)

    # 市值加权指标计算
    weighted_dividend_sum = 0
    weighted_pb_sum = 0
    weighted_pe_sum = 0
    valid_dividend_market_value = 0
    valid_pb_market_value = 0
    valid_pe_market_value = 0

    print("📊 开始计算市值加权指标...")
    print(f"总股票数: {stock_count}")
    print(f"总市值: {total_market_value:,.2f}元")
    print("-" * 60)

    for code, stock in stock_data.items():
        market_value = stock.get('market_value', 0)
        name = stock.get('name', '未知')
        
        if market_value > 0:
            print(f"{name} ({code}): 市值 {market_value:,.2f}元")
            
            # 股息率TTM
            dividend_yield = stock.get('dividend_yield', 0)
            if dividend_yield and dividend_yield > 0:
                weighted_dividend_sum += dividend_yield * market_value
                valid_dividend_market_value += market_value
                print(f"  股息率: {dividend_yield}% (权重: {market_value/total_market_value*100:.1f}%)")

            # 市净率PB
            pb_ratio = stock.get('pb_ratio', 0)
            if pb_ratio and pb_ratio > 0:
                weighted_pb_sum += pb_ratio * market_value
                valid_pb_market_value += market_value
                print(f"  市净率: {pb_ratio} (权重: {market_value/total_market_value*100:.1f}%)")

            # 市盈率TTM
            pe_ttm = stock.get('pe_ratio', 0)  # 修正字段名
            if pe_ttm and pe_ttm > 0:
                weighted_pe_sum += pe_ttm * market_value
                valid_pe_market_value += market_value
                print(f"  市盈率TTM: {pe_ttm} (权重: {market_value/total_market_value*100:.1f}%)")
            
            print()

    # 计算市值加权平均值
    market_weighted_dividend_yield_ttm = (weighted_dividend_sum / valid_dividend_market_value) if valid_dividend_market_value > 0 else 0
    market_weighted_pb_ratio = (weighted_pb_sum / valid_pb_market_value) if valid_pb_market_value > 0 else 0
    market_weighted_pe_ttm = (weighted_pe_sum / valid_pe_market_value) if valid_pe_market_value > 0 else 0

    print("=" * 60)
    print("📈 市值加权指标计算结果:")
    print(f"市值加权股息率TTM: {market_weighted_dividend_yield_ttm:.2f}%")
    print(f"市值加权市净率: {market_weighted_pb_ratio:.2f}")
    print(f"市值加权市盈率TTM: {market_weighted_pe_ttm:.2f}")
    print()
    print("📊 统计信息:")
    print(f"有股息数据的股票: {len([s for s in stock_data.values() if s.get('dividend_yield', 0) > 0])}只")
    print(f"有PB数据的股票: {len([s for s in stock_data.values() if s.get('pb_ratio', 0) > 0])}只")
    print(f"有PE数据的股票: {len([s for s in stock_data.values() if s.get('pe_ttm', 0) > 0])}只")

    return {
        'total_market_value': total_market_value,
        'stock_count': stock_count,
        'market_weighted_dividend_yield_ttm': round(market_weighted_dividend_yield_ttm, 2),
        'market_weighted_pb_ratio': round(market_weighted_pb_ratio, 2),
        'market_weighted_pe_ttm': round(market_weighted_pe_ttm, 2),
        'valid_dividend_stocks': len([s for s in stock_data.values() if s.get('dividend_yield', 0) > 0]),
        'valid_pb_stocks': len([s for s in stock_data.values() if s.get('pb_ratio', 0) > 0]),
        'valid_pe_stocks': len([s for s in stock_data.values() if s.get('pe_ratio', 0) > 0])  # 修正字段名
    }

def test_with_sample_data():
    """使用示例数据测试"""
    print("🧪 使用示例数据测试市值加权指标计算")
    print("=" * 60)
    
    # 示例股票数据
    sample_data = {
        '000001': {
            'name': '平安银行',
            'price': 12.50,
            'holdings': 1000,
            'market_value': 12500,
            'dividend_yield': 3.2,
            'pb_ratio': 0.8,
            'pe_ratio': 5.5
        },
        '000002': {
            'name': '万科A',
            'price': 8.90,
            'holdings': 2000,
            'market_value': 17800,
            'dividend_yield': 4.1,
            'pb_ratio': 1.2,
            'pe_ratio': 8.3
        },
        '600519': {
            'name': '贵州茅台',
            'price': 1680.00,
            'holdings': 100,
            'market_value': 168000,
            'dividend_yield': 1.8,
            'pb_ratio': 12.5,
            'pe_ratio': 28.6
        },
        '000858': {
            'name': '五粮液',
            'price': 128.50,
            'holdings': 500,
            'market_value': 64250,
            'dividend_yield': 2.3,
            'pb_ratio': 4.2,
            'pe_ratio': 18.9
        }
    }
    
    result = calculate_market_weighted_metrics(sample_data)
    
    print("\n🎯 最终结果:")
    print(f"总市值: {result['total_market_value']:,.2f}元")
    print(f"市值加权股息率TTM: {result['market_weighted_dividend_yield_ttm']}%")
    print(f"市值加权市净率: {result['market_weighted_pb_ratio']}")
    print(f"市值加权市盈率TTM: {result['market_weighted_pe_ttm']}")

def test_with_real_data():
    """使用真实数据测试（如果存在）"""
    import json
    import os
    
    print("\n🔍 尝试加载真实股票数据...")
    
    # 尝试加载真实数据
    data_file = 'stock_data_cache.json'
    if os.path.exists(data_file):
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            stock_data = data.get('stock_data', {})
            if stock_data:
                print(f"✅ 成功加载 {len(stock_data)} 只股票的真实数据")
                result = calculate_market_weighted_metrics(stock_data)
                
                print("\n🎯 真实数据计算结果:")
                print(f"总市值: {result['total_market_value']:,.2f}元")
                print(f"市值加权股息率TTM: {result['market_weighted_dividend_yield_ttm']}%")
                print(f"市值加权市净率: {result['market_weighted_pb_ratio']}")
                print(f"市值加权市盈率TTM: {result['market_weighted_pe_ttm']}")
                return result
            else:
                print("⚠️ 数据文件中没有股票数据")
        except Exception as e:
            print(f"❌ 加载真实数据失败: {e}")
    else:
        print("⚠️ 未找到真实数据文件")
    
    return None

if __name__ == "__main__":
    # 测试示例数据
    test_with_sample_data()
    
    # 测试真实数据
    test_with_real_data()
