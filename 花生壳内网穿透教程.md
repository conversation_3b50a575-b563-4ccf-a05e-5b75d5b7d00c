# 🥜 花生壳内网穿透详细教程

## 🎯 为什么选择花生壳？

- ✅ **完全免费**：提供免费域名和流量
- ✅ **中国本土**：服务器在国内，访问速度快
- ✅ **操作简单**：图形界面，一键配置
- ✅ **稳定可靠**：老牌服务商，技术成熟
- ✅ **无需公网IP**：家庭宽带也能用

## 📋 详细操作步骤

### 第1步：注册花生壳账号

1. **访问官网**：https://hsk.oray.com
2. **点击注册**：使用手机号注册
3. **实名认证**：上传身份证（必须步骤）
4. **获得免费域名**：系统自动分配

### 第2步：下载安装客户端

1. **下载客户端**：
   - Windows：https://hsk.oray.com/download/
   - 选择"花生壳客户端"
   - 大小约20MB

2. **安装步骤**：
   - 双击安装包
   - 一路点击"下一步"
   - 安装完成后会自动启动

### 第3步：登录并配置

1. **登录账号**：
   - 输入注册的手机号和密码
   - 点击"登录"

2. **查看免费域名**：
   - 登录后可以看到分配的免费域名
   - 格式：`xxxxx.vicp.fun` 或 `xxxxx.vipnps.com`

### 第4步：添加内网穿透

1. **点击"内网穿透"**
2. **添加映射**：
   - 应用名称：`持仓系统`
   - 内网主机：`127.0.0.1`
   - 内网端口：`5000`
   - 外网域名：选择你的免费域名
   - 点击"确定"

### 第5步：启动你的持仓系统

1. **打开命令行**（在你的项目文件夹）
2. **运行系统**：
   ```bash
   python 持仓系统_v14.py
   ```
3. **确认启动**：看到"🚀 启动持仓系统..."

### 第6步：测试访问

1. **获取外网地址**：
   - 在花生壳客户端中查看
   - 格式：`http://你的域名.vicp.fun`

2. **测试访问**：
   - 在浏览器中输入外网地址
   - 应该能看到你的持仓系统

## 🔧 常见问题解决

### 问题1：无法访问外网地址
**解决方案**：
- 检查本地系统是否正常启动
- 确认端口5000没有被占用
- 重启花生壳客户端

### 问题2：访问速度慢
**解决方案**：
- 免费版有带宽限制，升级付费版可提速
- 选择离你更近的服务器节点

### 问题3：域名被拦截
**解决方案**：
- 某些网络可能拦截动态域名
- 可以申请备案域名（需要付费）

## 💰 费用说明

### 免费版限制
- **带宽**：1Mbps
- **流量**：1GB/月
- **并发连接**：2个
- **域名**：二级域名

### 付费版优势
- **带宽**：10Mbps起
- **流量**：不限
- **并发连接**：不限
- **域名**：可绑定自定义域名

**对于个人使用，免费版完全够用！**

## 🛡️ 安全设置

### 1. 设置访问密码
在你的代码中添加简单认证：

```python
# 在持仓系统_v14.py中添加
from flask import request, session, redirect, url_for

# 设置密码
ACCESS_PASSWORD = "你的访问密码"

@app.before_request
def check_login():
    # 排除登录页面
    if request.endpoint == 'login':
        return
    
    # 检查是否已登录
    if 'logged_in' not in session:
        return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password')
        if password == ACCESS_PASSWORD:
            session['logged_in'] = True
            return redirect('/')
        else:
            return '密码错误'
    
    return '''
    <form method="post">
        <h2>请输入访问密码</h2>
        <input type="password" name="password" required>
        <button type="submit">登录</button>
    </form>
    '''
```

### 2. 限制访问时间
```python
from datetime import datetime

@app.before_request
def check_time():
    # 只允许工作时间访问（9:00-18:00）
    current_hour = datetime.now().hour
    if current_hour < 9 or current_hour > 18:
        return '非工作时间，系统暂停访问'
```

## 🎉 完成！

现在你的持仓系统已经可以通过互联网访问了！

**你的系统地址**：`http://你的域名.vicp.fun`

**分享给朋友**：把这个网址发给朋友，他们就能看到你的股票数据了！

## 📱 手机访问

花生壳的域名在手机上也能正常访问，你可以：
- 用手机浏览器打开网址
- 随时随地查看股票数据
- 分享给朋友查看

## 🔄 维护建议

1. **保持电脑开机**：系统需要在你的电脑上运行
2. **定期检查**：确保花生壳客户端正常运行
3. **备份数据**：定期备份重要的股票数据
4. **更新系统**：及时更新持仓系统版本
