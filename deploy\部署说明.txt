🚀 腾讯云CloudBase部署包
========================

📁 文件说明：
- main.py                    启动文件
- 持仓系统_简化版.py          简化版主程序（推荐）
- 持仓系统_v14.py            完整版主程序
- requirements.txt           依赖包列表
- Dockerfile                 容器配置文件
- 其他.py文件                依赖模块

🎯 部署步骤：

1. 将这个文件夹打包成ZIP文件
2. 登录腾讯云CloudBase控制台
3. 选择"云托管" -> "新建服务"
4. 选择"通过本地代码部署"
5. 填写配置：
   - 代码包类型: 压缩包
   - 代码包: 上传ZIP文件
   - 服务名称: stock-system
   - 端口: 8080
   - Dockerfile名称: Dockerfile
6. 点击"部署"
7. 等待3-5分钟完成部署

✅ 部署成功后：
- 你会得到一个访问网址
- 可以分享给别人访问
- 24小时运行，无需开机

💡 注意事项：
- 确保选择"压缩包"而不是"文件夹"
- 端口必须填写8080
- 如有问题，查看部署日志

🎉 祝你部署成功！
