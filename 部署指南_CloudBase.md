# 持仓系统V14 CloudBase部署指南

## 📋 改造完成清单

### ✅ 已完成的改造

1. **环境兼容性**
   - ✅ 添加CloudBase环境检测 (`IS_CLOUDBASE`)
   - ✅ 适配临时目录存储 (`TEMP_DIR`)
   - ✅ 环境变量配置优化
   - ✅ 日志系统适配

2. **文件存储适配**
   - ✅ 上传目录使用临时目录
   - ✅ 数据持久化跳过文件操作（CloudBase环境）
   - ✅ 缓存文件路径适配

3. **应用启动适配**
   - ✅ 禁用后台自动更新线程
   - ✅ CloudBase入口函数 (`main_handler`)
   - ✅ WSGI环境构造
   - ✅ 错误处理优化

4. **依赖模块兼容**
   - ✅ 可选导入处理（交易时间监测、年内最低价缓存、扫雷模块）
   - ✅ 简化版替代实现
   - ✅ 异常处理增强

5. **配置文件**
   - ✅ `cloudbaserc_v14.json` - 主应用配置
   - ✅ `cloudbaserc_test.json` - 测试应用配置
   - ✅ `requirements.txt` - 依赖列表
   - ✅ 部署脚本（Windows/Linux）

6. **测试和文档**
   - ✅ `test_cloudbase.py` - 测试应用
   - ✅ `portfolio_system_v14.py` - 英文文件名wrapper
   - ✅ 部署指南和README

## 🚀 部署步骤

### 方法一：测试部署（推荐先执行）

1. **部署测试应用**
   ```bash
   # 使用测试配置
   cp cloudbaserc_test.json cloudbaserc.json
   tcb functions:deploy portfolio-test
   ```

2. **验证测试应用**
   - 访问: `https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/`
   - 检查API: `/api/test`, `/api/env`, `/api/status`

### 方法二：完整部署

1. **使用部署脚本**
   ```bash
   # Windows
   deploy_cloudbase_v14.bat
   
   # Linux/Mac
   chmod +x deploy_cloudbase_v14.sh
   ./deploy_cloudbase_v14.sh
   ```

2. **手动部署**
   ```bash
   cp cloudbaserc_v14.json cloudbaserc.json
   tcb functions:deploy portfolio-system-v14
   ```

## 🔧 配置说明

### 主要配置项

- **运行时**: Python 3.9
- **内存**: 1024MB（主应用）/ 512MB（测试应用）
- **超时**: 120秒（主应用）/ 60秒（测试应用）
- **处理器**: `持仓系统_v14.main_handler`

### 环境变量

```json
{
  "WECHAT_WEBHOOK_URL": "企业微信机器人地址",
  "TENCENTCLOUD_RUNENV": "SCF",
  "FLASK_ENV": "production"
}
```

## 📊 功能对比

| 功能 | 本地版本 | CloudBase版本 | 说明 |
|------|----------|---------------|------|
| 股票数据获取 | ✅ | ✅ | 完全支持 |
| 卖出策略分析 | ✅ | ✅ | 完全支持 |
| Excel上传 | ✅ | ✅ | 使用临时存储 |
| 数据可视化 | ✅ | ✅ | 完全支持 |
| 企业微信提醒 | ✅ | ✅ | 完全支持 |
| 后台自动更新 | ✅ | ❌ | 云函数特性限制 |
| 数据持久化 | 文件系统 | 内存 | 会话级别 |
| 扫雷功能 | ✅ | ⚠️ | 简化版本 |

## 🔍 故障排除

### 常见问题

1. **部署失败**
   ```bash
   # 检查登录状态
   tcb auth:list
   
   # 重新登录
   tcb login
   ```

2. **函数超时**
   - 增加超时时间配置
   - 检查网络请求性能
   - 优化数据处理逻辑

3. **内存不足**
   - 增加内存配置到1024MB或更高
   - 优化数据结构
   - 分批处理大量数据

4. **依赖模块缺失**
   - 确保所有依赖文件都已上传
   - 检查`requirements.txt`是否完整
   - 使用简化版替代实现

### 调试命令

```bash
# 查看函数日志
tcb functions:log portfolio-system-v14 --tail

# 查看函数详情
tcb functions:detail portfolio-system-v14

# 更新函数配置
tcb functions:config:update portfolio-system-v14
```

## 📈 性能优化建议

1. **冷启动优化**
   - 减少导入的模块数量
   - 使用全局变量缓存数据
   - 优化初始化逻辑

2. **内存使用优化**
   - 及时清理不需要的数据
   - 使用生成器处理大数据集
   - 避免重复加载相同数据

3. **网络请求优化**
   - 设置合理的超时时间
   - 使用连接池
   - 实现请求重试机制

## 🔄 版本更新

更新应用时：

```bash
# 重新部署
tcb functions:deploy portfolio-system-v14

# 查看部署状态
tcb functions:list
```

## 📞 技术支持

如遇问题，请检查：
1. CloudBase CLI版本是否最新
2. 腾讯云账户权限是否充足
3. 网络连接是否正常
4. 配置文件格式是否正确

---

**注意**: CloudBase版本保持了原有的所有核心功能，主要差异在于数据持久化和后台任务处理方式。对于生产环境使用，建议先在测试环境验证所有功能正常。
