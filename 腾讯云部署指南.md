# 腾讯云 CloudBase 部署指南

## 🎯 为什么选择腾讯云 CloudBase？

- ✅ **完全免费**：每月免费额度足够个人使用
- ✅ **中国大陆访问快**：服务器在国内，访问速度很快
- ✅ **一键部署**：支持GitHub直接部署
- ✅ **中文支持**：完全中文界面和文档
- ✅ **稳定可靠**：腾讯云的基础设施

## 📋 部署步骤

### 1. 注册腾讯云账号
1. 访问：https://cloud.tencent.com
2. 点击"免费注册"
3. 使用手机号注册（需要实名认证）

### 2. 开通 CloudBase
1. 登录后搜索"CloudBase"
2. 点击"立即使用"
3. 创建环境（选择"按量计费"，有免费额度）

### 3. 部署应用
1. 在CloudBase控制台点击"云托管"
2. 选择"从代码仓库部署"
3. 连接你的GitHub仓库
4. 选择分支（main）
5. 配置构建：
   - 构建命令：`pip install -r requirements.txt`
   - 启动命令：`python start_cloud.py`
   - 端口：80
6. 点击"部署"

### 4. 配置域名
- CloudBase会自动分配一个域名
- 格式：`https://你的应用名-xxx.ap-shanghai.app.tcloudbase.com`
- 也可以绑定自定义域名

## 💰 费用说明

### 免费额度（每月）
- CPU：180,000 核秒
- 内存：360,000 MB秒
- 流量：1GB
- 存储：5GB

**对于个人使用完全够用！**

## 🔧 配置文件

需要创建 `cloudbaserc.json` 配置文件：

```json
{
  "envId": "你的环境ID",
  "framework": {
    "name": "flask",
    "plugins": {
      "node": {
        "use": "@cloudbase/framework-plugin-node",
        "inputs": {
          "entry": "start_cloud.py",
          "runtime": "Python3.7"
        }
      }
    }
  }
}
```

## 🚀 优势对比

| 平台 | 中国访问 | 免费额度 | 部署难度 | 中文支持 |
|------|----------|----------|----------|----------|
| 腾讯云CloudBase | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Railway | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| Render | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |

## 📞 技术支持

- 腾讯云有中文客服
- 工单系统响应快
- 社区活跃，中文资料多
