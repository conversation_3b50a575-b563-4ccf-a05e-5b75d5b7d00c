#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统云端版 - 包含真实功能
============================
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import pandas as pd
import os
from datetime import datetime
import json

app = Flask(__name__)

# 模拟持仓数据（你可以后续替换为真实数据）
SAMPLE_HOLDINGS = [
    {
        'code': '000001',
        'name': '平安银行',
        'shares': 1000,
        'cost_price': 12.50,
        'current_price': 13.20,
        'market_value': 13200,
        'profit_loss': 700,
        'profit_rate': 5.6
    },
    {
        'code': '000002',
        'name': '万科A',
        'shares': 500,
        'cost_price': 18.30,
        'current_price': 17.80,
        'market_value': 8900,
        'profit_loss': -250,
        'profit_rate': -2.7
    },
    {
        'code': '600036',
        'name': '招商银行',
        'shares': 800,
        'cost_price': 35.20,
        'current_price': 36.50,
        'market_value': 29200,
        'profit_loss': 1040,
        'profit_rate': 3.7
    }
]

@app.route('/')
def index():
    """主页 - 显示持仓数据"""
    html_template = '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>持仓系统</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 { font-size: 2.5em; margin-bottom: 10px; }
            .header p { font-size: 1.2em; opacity: 0.9; }
            
            .summary {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                padding: 30px;
                background: #f8f9fa;
            }
            .summary-item {
                text-align: center;
                padding: 20px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .summary-item h3 { color: #495057; margin-bottom: 10px; }
            .summary-item .value { font-size: 1.5em; font-weight: bold; }
            .profit { color: #28a745; }
            .loss { color: #dc3545; }
            
            .holdings {
                padding: 30px;
            }
            .holdings h2 {
                color: #333;
                margin-bottom: 20px;
                text-align: center;
            }
            .stock-table {
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .stock-table th {
                background: #495057;
                color: white;
                padding: 15px;
                text-align: center;
            }
            .stock-table td {
                padding: 12px;
                text-align: center;
                border-bottom: 1px solid #dee2e6;
            }
            .stock-table tr:hover {
                background: #f8f9fa;
            }
            .stock-code { font-weight: bold; color: #007bff; }
            .stock-name { font-weight: bold; }
            
            .footer {
                text-align: center;
                padding: 20px;
                color: #6c757d;
                background: #f8f9fa;
            }
            
            @media (max-width: 768px) {
                .container { margin: 10px; }
                .header h1 { font-size: 2em; }
                .stock-table { font-size: 0.9em; }
                .stock-table th, .stock-table td { padding: 8px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 持仓系统</h1>
                <p>实时监控 · 智能分析 · 云端部署</p>
            </div>
            
            <div class="summary">
                <div class="summary-item">
                    <h3>📊 总市值</h3>
                    <div class="value">¥{{ "%.2f"|format(total_value) }}</div>
                </div>
                <div class="summary-item">
                    <h3>💰 总盈亏</h3>
                    <div class="value {{ 'profit' if total_profit > 0 else 'loss' }}">
                        {{ "+" if total_profit > 0 else "" }}¥{{ "%.2f"|format(total_profit) }}
                    </div>
                </div>
                <div class="summary-item">
                    <h3>📈 盈亏比例</h3>
                    <div class="value {{ 'profit' if total_rate > 0 else 'loss' }}">
                        {{ "+" if total_rate > 0 else "" }}{{ "%.2f"|format(total_rate) }}%
                    </div>
                </div>
                <div class="summary-item">
                    <h3>🔢 持仓数量</h3>
                    <div class="value">{{ holdings|length }} 只</div>
                </div>
            </div>
            
            <div class="holdings">
                <h2>📋 持仓明细</h2>
                <table class="stock-table">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>持仓数量</th>
                            <th>成本价</th>
                            <th>现价</th>
                            <th>市值</th>
                            <th>盈亏金额</th>
                            <th>盈亏比例</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stock in holdings %}
                        <tr>
                            <td class="stock-code">{{ stock.code }}</td>
                            <td class="stock-name">{{ stock.name }}</td>
                            <td>{{ stock.shares }}</td>
                            <td>¥{{ "%.2f"|format(stock.cost_price) }}</td>
                            <td>¥{{ "%.2f"|format(stock.current_price) }}</td>
                            <td>¥{{ "%.2f"|format(stock.market_value) }}</td>
                            <td class="{{ 'profit' if stock.profit_loss > 0 else 'loss' }}">
                                {{ "+" if stock.profit_loss > 0 else "" }}¥{{ "%.2f"|format(stock.profit_loss) }}
                            </td>
                            <td class="{{ 'profit' if stock.profit_rate > 0 else 'loss' }}">
                                {{ "+" if stock.profit_rate > 0 else "" }}{{ "%.2f"|format(stock.profit_rate) }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="footer">
                <p>📱 更新时间: {{ update_time }} | 🌐 腾讯云CloudBase部署</p>
                <p style="margin-top: 10px; font-size: 0.9em;">
                    💡 这是演示数据，如需接入真实股票数据，请联系开发者
                </p>
            </div>
        </div>
    </body>
    </html>
    '''
    
    # 计算汇总数据
    total_value = sum(stock['market_value'] for stock in SAMPLE_HOLDINGS)
    total_profit = sum(stock['profit_loss'] for stock in SAMPLE_HOLDINGS)
    total_cost = total_value - total_profit
    total_rate = (total_profit / total_cost * 100) if total_cost > 0 else 0
    
    return render_template_string(
        html_template,
        holdings=SAMPLE_HOLDINGS,
        total_value=total_value,
        total_profit=total_profit,
        total_rate=total_rate,
        update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )

@app.route('/api/holdings')
def api_holdings():
    """获取持仓数据API"""
    return jsonify({
        'status': 'success',
        'data': SAMPLE_HOLDINGS,
        'summary': {
            'total_value': sum(stock['market_value'] for stock in SAMPLE_HOLDINGS),
            'total_profit': sum(stock['profit_loss'] for stock in SAMPLE_HOLDINGS),
            'count': len(SAMPLE_HOLDINGS)
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 启动持仓系统（功能版）...")
    
    port = int(os.environ.get('PORT', 8080))
    print(f"🌐 启动成功，端口: {port}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,
        threaded=True
    )
