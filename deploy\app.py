#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统云端版 - 包含真实功能
============================
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import pandas as pd
import os
import sys
from datetime import datetime
import json

# 添加上级目录到Python路径，以便访问真实数据
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

app = Flask(__name__)

def load_real_stock_data():
    """加载真实的股票数据"""
    try:
        # 尝试从上级目录加载数据
        cache_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'stock_data_cache.json')
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('stock_data', {})

        print("⚠️ 未找到股票数据缓存文件")
        return {}
    except Exception as e:
        print(f"❌ 加载股票数据失败: {e}")
        return {}

def convert_to_web_format(stock_data):
    """将股票数据转换为Web显示格式"""
    holdings = []
    total_market_value = 0
    total_profit_loss = 0

    for code, data in stock_data.items():
        if data.get('holdings', 0) > 0:  # 只显示有持仓的股票
            market_value = data.get('market_value', 0)
            unit_cost = data.get('unit_cost', 0)
            current_price = data.get('price', 0)
            holdings_count = data.get('holdings', 0)

            # 计算盈亏
            if unit_cost > 0 and current_price > 0:
                cost_total = unit_cost * holdings_count
                profit_loss = market_value - cost_total
                profit_rate = (profit_loss / cost_total) * 100 if cost_total > 0 else 0
            else:
                profit_loss = 0
                profit_rate = 0

            holdings.append({
                'code': code,
                'name': data.get('name', ''),
                'shares': holdings_count,
                'cost_price': unit_cost,
                'current_price': current_price,
                'market_value': market_value,
                'profit_loss': profit_loss,
                'profit_rate': profit_rate,
                'change_pct': data.get('change_pct', 0),
                'industry': data.get('industry', ''),
                'update_time': data.get('update_time', '')
            })

            total_market_value += market_value
            total_profit_loss += profit_loss

    return holdings, total_market_value, total_profit_loss

@app.route('/')
def index():
    """主页 - 显示持仓数据"""
    html_template = '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>持仓系统</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                padding: 20px;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                overflow: hidden;
            }
            .header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 30px;
                text-align: center;
            }
            .header h1 { font-size: 2.5em; margin-bottom: 10px; }
            .header p { font-size: 1.2em; opacity: 0.9; }

            .summary {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                padding: 30px;
                background: #f8f9fa;
            }
            .summary-item {
                text-align: center;
                padding: 20px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .summary-item h3 { color: #495057; margin-bottom: 10px; }
            .summary-item .value { font-size: 1.5em; font-weight: bold; }
            .profit { color: #28a745; }
            .loss { color: #dc3545; }

            .holdings {
                padding: 30px;
            }
            .holdings h2 {
                color: #333;
                margin-bottom: 20px;
                text-align: center;
            }
            .stock-table {
                width: 100%;
                border-collapse: collapse;
                background: white;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .stock-table th {
                background: #495057;
                color: white;
                padding: 15px;
                text-align: center;
            }
            .stock-table td {
                padding: 12px;
                text-align: center;
                border-bottom: 1px solid #dee2e6;
            }
            .stock-table tr:hover {
                background: #f8f9fa;
            }
            .stock-code { font-weight: bold; color: #007bff; }
            .stock-name { font-weight: bold; }

            .footer {
                text-align: center;
                padding: 20px;
                color: #6c757d;
                background: #f8f9fa;
            }

            @media (max-width: 768px) {
                .container { margin: 10px; }
                .header h1 { font-size: 2em; }
                .stock-table { font-size: 0.9em; }
                .stock-table th, .stock-table td { padding: 8px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 持仓系统</h1>
                <p>实时监控 · 智能分析 · 云端部署</p>
            </div>

            <div class="summary">
                <div class="summary-item">
                    <h3>📊 总市值</h3>
                    <div class="value">¥{{ "%.2f"|format(total_value) }}</div>
                </div>
                <div class="summary-item">
                    <h3>💰 总盈亏</h3>
                    <div class="value {{ 'profit' if total_profit > 0 else 'loss' }}">
                        {{ "+" if total_profit > 0 else "" }}¥{{ "%.2f"|format(total_profit) }}
                    </div>
                </div>
                <div class="summary-item">
                    <h3>📈 盈亏比例</h3>
                    <div class="value {{ 'profit' if total_rate > 0 else 'loss' }}">
                        {{ "+" if total_rate > 0 else "" }}{{ "%.2f"|format(total_rate) }}%
                    </div>
                </div>
                <div class="summary-item">
                    <h3>🔢 持仓数量</h3>
                    <div class="value">{{ holdings|length }} 只</div>
                </div>
            </div>

            <div class="holdings">
                <h2>📋 持仓明细</h2>
                <table class="stock-table">
                    <thead>
                        <tr>
                            <th>股票代码</th>
                            <th>股票名称</th>
                            <th>持仓数量</th>
                            <th>成本价</th>
                            <th>现价</th>
                            <th>市值</th>
                            <th>盈亏金额</th>
                            <th>盈亏比例</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stock in holdings %}
                        <tr>
                            <td class="stock-code">{{ stock.code }}</td>
                            <td class="stock-name">{{ stock.name }}</td>
                            <td>{{ stock.shares }}</td>
                            <td>¥{{ "%.2f"|format(stock.cost_price) }}</td>
                            <td>¥{{ "%.2f"|format(stock.current_price) }}</td>
                            <td>¥{{ "%.2f"|format(stock.market_value) }}</td>
                            <td class="{{ 'profit' if stock.profit_loss > 0 else 'loss' }}">
                                {{ "+" if stock.profit_loss > 0 else "" }}¥{{ "%.2f"|format(stock.profit_loss) }}
                            </td>
                            <td class="{{ 'profit' if stock.profit_rate > 0 else 'loss' }}">
                                {{ "+" if stock.profit_rate > 0 else "" }}{{ "%.2f"|format(stock.profit_rate) }}%
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <div class="footer">
                <p>📱 更新时间: {{ update_time }} | 🌐 腾讯云CloudBase部署</p>
                <p style="margin-top: 10px; font-size: 0.9em;">
                    ✅ 实时监控·智能分析·云端部署
                </p>
            </div>
        </div>
    </body>
    </html>
    '''

    # 加载真实股票数据
    stock_data = load_real_stock_data()
    holdings, total_market_value, total_profit_loss = convert_to_web_format(stock_data)

    # 计算汇总数据
    total_cost = total_market_value - total_profit_loss if total_market_value > total_profit_loss else total_market_value
    total_rate = (total_profit_loss / total_cost * 100) if total_cost > 0 else 0

    return render_template_string(
        html_template,
        holdings=holdings,
        total_value=total_market_value,
        total_profit=total_profit_loss,
        total_rate=total_rate,
        update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )

@app.route('/api/holdings')
def api_holdings():
    """获取持仓数据API"""
    try:
        # 加载真实股票数据
        stock_data = load_real_stock_data()
        holdings, total_market_value, total_profit_loss = convert_to_web_format(stock_data)

        return jsonify({
            'status': 'success',
            'data': holdings,
            'summary': {
                'total_value': round(total_market_value, 2),
                'total_profit': round(total_profit_loss, 2),
                'count': len(holdings),
                'profit_rate': round((total_profit_loss / (total_market_value - total_profit_loss) * 100) if total_market_value > total_profit_loss else 0, 2)
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 启动持仓系统（功能版）...")

    port = int(os.environ.get('PORT', 8080))
    print(f"🌐 启动成功，端口: {port}")

    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,
        threaded=True
    )
    port = int(os.environ.get('PORT', 8080))
    
    # 启动应用
    print(f"🌐 启动成功，端口: {port}")
    app.run(
        host='0.0.0.0',
        port=port,
        debug=False,
        threaded=True
    )
