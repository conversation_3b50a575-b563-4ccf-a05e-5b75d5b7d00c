# 🚀 腾讯云CloudBase快速部署指南

## 🎯 你现在看到的就是"通过本地代码部署"

从你发的截图可以看到，腾讯云CloudBase提供了多种部署方式，其中**"通过本地代码部署"**就是你要找的功能！

## 📋 准备工作（5分钟）

### 1. 确保文件齐全
你的项目现在已经准备好了：
- ✅ `持仓系统_v14.py` - 主程序（已适配8080端口）
- ✅ `requirements.txt` - 依赖列表
- ✅ `Dockerfile` - 容器配置（已更新）

### 2. 注册腾讯云账号
- 访问：https://cloud.tencent.com
- 注册并完成实名认证（需要身份证）

## 🚀 超简单3步部署

### 第1步：开通CloudBase环境

1. **搜索CloudBase**：
   - 登录腾讯云控制台
   - 搜索"CloudBase"或"云开发"

2. **创建环境**：
   - 点击"立即使用"
   - 环境名称：`stock-system`
   - 计费方式：选择"按量计费"（有免费额度）
   - 点击"立即开通"

3. **记住环境ID**：
   - 创建完成后会显示环境ID
   - 格式类似：`stock-system-xxx`

### 第2步：部署应用

1. **进入云托管**：
   - 在CloudBase控制台点击"云托管"
   - 点击"新建服务"

2. **选择部署方式**：
   - 选择"**通过本地代码部署**"（就是你截图中看到的）
   - 部署方式：选择"上传代码包"
   - 代码包类型：选择"文件夹"

3. **上传代码**：
   - 点击"选择文件夹"
   - 选择你的项目文件夹（包含持仓系统_v14.py的文件夹）
   - 等待上传完成

4. **配置服务**：
   - 服务名称：`stock-system`
   - 端口：`8080`
   - CPU：`0.25核`
   - 内存：`0.5GB`
   - 最小副本数：`0`（节省费用）
   - 最大副本数：`10`

5. **点击创建**：
   - 等待部署完成（约3-5分钟）

### 第3步：获取访问地址

1. **查看服务状态**：
   - 部署完成后，状态显示为"正常"

2. **获取访问地址**：
   - 点击服务名称进入详情
   - 在"基本信息"中找到"访问地址"
   - 格式：`https://stock-system-xxx.ap-shanghai.run.tcloudbase.com`

3. **测试访问**：
   - 在浏览器中打开这个地址
   - 应该能看到你的持仓系统！

## 🎉 部署成功！

**恭喜！你的持仓系统现在可以通过互联网访问了！**

### 你得到了什么：
- 🌐 **公网访问地址**：任何人都能访问
- 🇨🇳 **中国用户友好**：腾讯云服务器在国内，速度快
- 💰 **免费使用**：每月免费额度足够个人使用
- 🔄 **自动扩缩容**：访问量大时自动增加服务器
- 📊 **监控日志**：完整的运行监控和日志

## 💰 费用说明

### 免费额度（每月）
- **CPU时间**：180,000 核秒
- **内存时间**：360,000 MB秒
- **流量**：1GB
- **构建时长**：100分钟

**按你的使用量，完全免费！**

## 🔧 如果遇到问题

### 部署失败
1. **检查代码**：确保所有文件都在项目文件夹中
2. **查看日志**：在CloudBase控制台查看构建日志
3. **检查端口**：确保使用8080端口

### 访问失败
1. **等待启动**：首次访问可能需要等待容器启动
2. **检查状态**：确保服务状态为"正常"
3. **查看日志**：检查应用运行日志

## 🛡️ 安全设置（可选）

### 1. 设置环境变量
在CloudBase控制台设置敏感信息：
- `WECHAT_WEBHOOK_URL`：你的企业微信密钥
- `SECRET_KEY`：应用密钥

### 2. 绑定自定义域名
- 可以绑定你自己的域名
- 需要域名备案（中国大陆）

## 🔄 更新部署

当你修改代码后：
1. 重新上传代码包
2. 或者使用CLI工具自动部署

## 📊 监控和管理

### 查看日志
- 在CloudBase控制台
- 点击"日志"查看应用运行情况

### 监控指标
- CPU使用率
- 内存使用率
- 请求量统计

## 🎯 总结

**腾讯云CloudBase "通过本地代码部署" 的优势：**

✅ **简单易用**：网页界面，拖拽上传
✅ **中国友好**：国内服务器，访问速度快
✅ **完全免费**：个人使用不花钱
✅ **专业运维**：自动扩缩容、监控、日志
✅ **安全可靠**：腾讯云基础设施

**vs 本地部署的区别：**
- 🌐 **24小时运行**：不需要你的电脑开机
- 📈 **自动扩容**：访问量大时自动增加服务器
- 🛡️ **专业运维**：腾讯云负责服务器维护
- ⚠️ **代码上云**：代码会上传到腾讯云服务器

**现在就开始部署吧！整个过程只需要10分钟！** 🚀
