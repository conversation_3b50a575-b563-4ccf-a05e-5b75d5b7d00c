# 持仓系统V14 腾讯云CloudBase部署指南

## 🎯 部署概述

将你的持仓系统V14部署到腾讯云CloudBase，实现云端访问和自动扩缩容。

## 📋 部署前准备

### 1. 安装腾讯云CLI工具
```bash
npm install -g @cloudbase/cli
```

### 2. 登录腾讯云
```bash
tcb login
```
按提示完成微信扫码登录

### 3. 创建云开发环境（如果还没有）
- 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
- 进入 **云开发 CloudBase**
- 点击 **新建环境**
- 选择 **按量计费**
- 记录环境ID（如：portfolio-system-9g8w6qhj8b8e8c8a）

## 🚀 一键部署

### 使用部署脚本（推荐）
```bash
# Windows用户
deploy.bat

# Linux/Mac用户
chmod +x deploy.sh && ./deploy.sh
```

### 手动部署步骤
```bash
# 1. 初始化项目
tcb init

# 2. 部署云函数
tcb functions:deploy portfolio-system

# 3. 查看部署状态
tcb functions:list

# 4. 查看函数详情
tcb functions:detail portfolio-system
```

## 📁 部署文件清单

确保以下文件都在项目根目录：

### 核心文件
- ✅ `portfolio_system_v14.py` - 主程序（已添加云函数入口）
- ✅ `requirements.txt` - Python依赖包
- ✅ `cloudbaserc.json` - 云开发配置文件
- ✅ `deploy.bat` - Windows部署脚本

### 依赖模块
- ✅ `A股交易时间监测_简化版.py`
- ✅ `yearly_low_cache_reader.py`
- ✅ `tdx_scan_module.py`

### 数据文件
- ✅ `stock_data_cache.json` - 股票数据缓存
- ✅ `imported_stock_list.json` - 导入股票列表

## ⚙️ 云函数配置

### 基础配置
- **运行时**: Python 3.7
- **内存**: 512MB
- **超时**: 60秒
- **并发**: 1000

### 环境变量
在腾讯云控制台 → 云函数 → 环境配置中设置：

| 变量名 | 值 | 说明 |
|--------|----|----|
| `FLASK_ENV` | `production` | 生产环境 |
| `PORT` | `8080` | 端口号 |
| `WECHAT_WEBHOOK_URL` | `你的企业微信机器人地址` | 消息推送 |

## 🌐 访问地址

部署成功后，你的持仓系统将在以下地址可用：

```
https://[环境ID].ap-shanghai.app.tcloudbase.com/portfolio-system
```

例如：
```
https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/
```

## 🔧 常见问题解决

### 1. 部署失败
```bash
# 检查登录状态
tcb auth:list

# 重新登录
tcb login

# 检查环境ID
tcb env:list
```

### 2. 函数超时
修改 `cloudbaserc.json`：
```json
{
  "functions": [{
    "timeout": 120,  // 增加到120秒
    "memorySize": 1024  // 增加内存到1GB
  }]
}
```

### 3. 依赖包安装失败
检查 `requirements.txt` 中的包版本：
```txt
Flask==2.3.3
requests==2.31.0
pandas==2.0.3
openpyxl==3.1.2
```

### 4. 数据文件丢失
云函数是无状态的，数据文件需要：
- 使用云数据库存储
- 或使用云存储COS
- 或在函数中重新生成

## 📊 监控和维护

### 查看函数日志
```bash
tcb functions:log portfolio-system
```

### 查看函数监控
- 登录腾讯云控制台
- 进入云函数
- 查看监控图表

### 更新部署
修改代码后重新部署：
```bash
tcb functions:deploy portfolio-system
```

## 💰 费用说明

### 免费额度（每月）
- 调用次数：100万次
- 资源使用量：40万GBs
- 外网出流量：1GB

### 超出免费额度后
- 调用次数：0.0133元/万次
- 资源使用量：0.000110592元/GBs
- 外网出流量：0.8元/GB

## 🎉 部署完成

部署成功后，你将拥有：

✅ **云端持仓系统** - 随时随地访问  
✅ **自动扩缩容** - 根据访问量自动调整  
✅ **高可用性** - 99.95%服务可用性  
✅ **HTTPS安全** - 自动SSL证书  
✅ **全球CDN** - 快速访问体验  

现在你可以通过云端地址访问你的持仓系统了！🚀
