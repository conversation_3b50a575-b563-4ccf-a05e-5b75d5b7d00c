#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
准备V14版本本地代码部署到腾讯云托管
"""

import os
import shutil
import json

def prepare_v14_deployment():
    """准备V14版本本地代码部署"""

    print("🚀 准备V14版本本地代码部署到腾讯云托管...")
    print("=" * 50)

    # V14版本的核心文件
    v14_files = [
        '持仓系统_v14.py',
        'A股交易时间监测_简化版.py',
        'yearly_low_cache_reader.py',
        'tdx_scan_module.py',
        'requirements.txt'
    ]

    # 数据文件
    data_files = [
        'stock_data_cache.json',
        'imported_stock_list.json',
        'strategy_config.json',
        'auto_update_config.json',
        'reduction_monitoring_config.json'
    ]

    # 检查V14核心文件
    print("📁 检查V14核心文件:")
    missing_files = []
    for file in v14_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - 缺失")
            missing_files.append(file)

    if missing_files:
        print(f"\n❌ 缺失关键文件: {missing_files}")
        return False

    # 检查数据文件
    print("\n📁 检查数据文件:")
    for file in data_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ⚠️ {file} - 可选文件不存在")

    # 创建app.py（重命名V14文件）
    print(f"\n📝 创建Flask应用文件:")
    shutil.copy2('持仓系统_v14.py', 'app.py')
    print(f"  ✅ 持仓系统_v14.py -> app.py")

    # 创建main.py入口文件
    main_py_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V14版本腾讯云托管入口文件
"""
import os
import sys

# 确保当前目录在Python路径中
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

if __name__ == "__main__":
    # 腾讯云托管会自动设置端口
    port = int(os.environ.get("PORT", 80))
    print(f"🚀 启动V14持仓系统，端口: {port}")
    app.run(host="0.0.0.0", port=port, debug=False)
'''

    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(main_py_content)
    print(f"  ✅ main.py (V14入口文件)")

    # 创建V14专用的Dockerfile
    dockerfile_content = '''FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p cache logs uploads data

# 暴露端口
EXPOSE 80

# 设置环境变量
ENV FLASK_ENV=production
ENV PYTHONPATH=/app
ENV PORT=80

# 启动命令
CMD ["python", "main.py"]
'''

    with open('Dockerfile', 'w', encoding='utf-8') as f:
        f.write(dockerfile_content)
    print(f"  ✅ Dockerfile (V14专用)")

    # 检查并创建必要的目录
    dirs_to_create = ['cache', 'logs', 'uploads', 'data']
    print(f"\n📁 创建必要目录:")
    for dir_name in dirs_to_create:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"  ✅ 创建目录: {dir_name}")
        else:
            print(f"  ✅ 目录已存在: {dir_name}")
    
    # 创建.gitignore文件
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# 日志文件
logs/
*.log

# 缓存文件
cache/
*.cache

# 上传文件
uploads/
*.xlsx
*.csv

# 配置文件
.env
*.pkl

# IDE
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
'''
    
    with open('.gitignore', 'w', encoding='utf-8') as f:
        f.write(gitignore_content)
    print(f"  ✅ 创建.gitignore文件")
    
    print("\n" + "=" * 50)
    print("🎉 V14版本本地代码部署准备完成！")
    print("\n📋 腾讯云托管配置信息:")
    print("  版本: 持仓系统V14")
    print("  主文件: app.py (从持仓系统_v14.py复制)")
    print("  入口文件: main.py")
    print("  Dockerfile名称: Dockerfile")
    print("  端口: 80")
    print("  启动命令: python main.py")

    print("\n📁 核心文件列表:")
    print("  ✅ app.py - V14主应用")
    print("  ✅ main.py - 入口文件")
    print("  ✅ Dockerfile - 容器配置")
    print("  ✅ A股交易时间监测_简化版.py")
    print("  ✅ yearly_low_cache_reader.py")
    print("  ✅ tdx_scan_module.py")
    print("  ✅ requirements.txt")

    print("\n🚀 腾讯云托管部署步骤:")
    print("1. 选择'通过本地代码部署'")
    print("2. 选择当前项目根目录")
    print("3. Dockerfile名称: Dockerfile")
    print("4. 端口: 80")
    print("5. 点击部署")

    print("\n⚠️ 重要提醒:")
    print("- 这是你的V14版本，包含所有原有功能")
    print("- 已自动适配腾讯云托管环境")
    print("- 端口已设置为80，符合腾讯云要求")

    return True

if __name__ == "__main__":
    prepare_v14_deployment()
