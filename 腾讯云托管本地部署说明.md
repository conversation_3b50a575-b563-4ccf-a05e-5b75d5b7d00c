# 腾讯云托管本地部署说明

## 🎯 当前状态
✅ 本地代码已准备完成，可以直接部署到腾讯云托管

## 📋 部署配置信息

### 基本配置
- **部署方式**: 通过本地代码部署
- **Dockerfile名称**: `Dockerfile`
- **端口**: `80`
- **服务类型**: 容器型服务

### 环境配置
- **Python版本**: 3.9
- **内存**: 512MB（推荐）
- **CPU**: 0.25核（推荐）

## 🚀 部署步骤

### 1. 选择部署方式
在腾讯云托管界面：
- 点击"通过本地代码部署"
- 选择当前项目根目录

### 2. 填写配置信息

#### 代码包类型
- 选择: **压缩包**

#### 服务相关
- **服务名称**: `portfolio-system`（或你喜欢的名称）
- **部署类型**: **容器型服务**

#### 容器配置
- **端口**: `80`
- **目标目录**: 留空（自动检测）
- **Dockerfile名称**: `Dockerfile`

#### 高级设置
- **环境变量**: 
  ```
  FLASK_ENV=production
  PYTHONPATH=/app
  PORT=80
  ```

- **入口点**: 数组输入
  ```
  gunicorn
  --bind
  0.0.0.0:80
  --workers
  1
  --timeout
  120
  app:app
  ```

- **命令**: 数组输入
  ```
  (留空，使用Dockerfile中的CMD)
  ```

### 3. 网络访问
- **公网访问**: 开启
- **内网访问**: 根据需要选择

### 4. 点击部署
- 点击"部署"按钮
- 等待部署完成（约3-5分钟）

## 📁 项目结构

当前根目录包含：
```
├── app.py                 # Flask主应用
├── main.py               # 入口文件
├── Dockerfile            # 容器配置
├── requirements.txt      # Python依赖
├── gunicorn_config.py    # Gunicorn配置
├── modules/              # 功能模块
├── templates/            # 前端模板
├── config/               # 配置文件
├── cache/                # 缓存目录
├── logs/                 # 日志目录
├── uploads/              # 上传目录
└── data/                 # 数据目录
```

## 🔧 关键文件说明

### Dockerfile
- 基于Python 3.9-slim
- 自动安装依赖
- 暴露80端口
- 使用gunicorn启动

### app.py
- Flask主应用文件
- 包含所有路由和功能
- 模块化架构

### requirements.txt
- 包含所有必要的Python依赖
- 版本已锁定，确保兼容性

## ⚠️ 注意事项

1. **端口配置**: 必须使用80端口
2. **Dockerfile**: 文件名必须是`Dockerfile`
3. **目录结构**: 不要移动核心文件
4. **依赖安装**: 会自动根据requirements.txt安装

## 🎉 部署成功后

部署成功后你将获得：
- 一个可访问的Web应用URL
- 完整的股票持仓管理系统
- 实时数据更新功能
- 多策略卖出信号提醒

## 🔍 故障排除

### 常见问题
1. **部署失败**: 检查Dockerfile语法
2. **启动失败**: 查看部署日志
3. **端口错误**: 确认使用80端口
4. **依赖错误**: 检查requirements.txt

### 查看日志
在腾讯云托管控制台可以查看：
- 构建日志
- 运行日志
- 错误日志

## 📞 支持

如果遇到问题，请检查：
1. 文件是否都在根目录
2. Dockerfile配置是否正确
3. 端口设置是否为80
4. 日志中的具体错误信息
