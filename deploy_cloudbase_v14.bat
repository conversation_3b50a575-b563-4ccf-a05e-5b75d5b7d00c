@echo off
chcp 65001 >nul
echo 🚀 开始部署持仓系统V14到腾讯云CloudBase...
echo.

REM 检查是否安装了CloudBase CLI
where tcb >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 未找到腾讯云CLI工具，请先安装：
    echo    npm install -g @cloudbase/cli
    pause
    exit /b 1
)

REM 检查是否已登录
echo 📝 检查登录状态...
tcb auth:list >nul 2>nul
if %errorlevel% neq 0 (
    echo 🔑 请先登录腾讯云：
    tcb login
)

REM 检查必要文件
echo 📋 检查部署文件...
if not exist "持仓系统_v14.py" (
    echo ❌ 未找到主程序文件：持仓系统_v14.py
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ 未找到依赖文件：requirements.txt
    pause
    exit /b 1
)

if not exist "cloudbaserc_v14.json" (
    echo ❌ 未找到配置文件：cloudbaserc_v14.json
    pause
    exit /b 1
)

REM 使用V14配置文件
echo 🔧 使用V14配置文件...
copy cloudbaserc_v14.json cloudbaserc.json >nul

REM 创建临时目录并复制必要文件
echo 📦 准备部署文件...
if exist temp_deploy rmdir /s /q temp_deploy
mkdir temp_deploy
copy "持仓系统_v14.py" temp_deploy\ >nul
copy requirements.txt temp_deploy\ >nul
copy cloudbaserc.json temp_deploy\ >nul

REM 复制依赖模块（如果存在）
if exist "A股交易时间监测_简化版.py" (
    copy "A股交易时间监测_简化版.py" temp_deploy\ >nul
    echo ✅ 已复制交易时间监测模块
)

if exist "yearly_low_cache_reader.py" (
    copy yearly_low_cache_reader.py temp_deploy\ >nul
    echo ✅ 已复制年内最低价缓存模块
)

if exist "tdx_scan_module.py" (
    copy tdx_scan_module.py temp_deploy\ >nul
    echo ✅ 已复制扫雷模块
)

REM 进入临时目录
cd temp_deploy

REM 部署函数
echo 📦 开始部署云函数...
tcb functions:deploy portfolio-system-v14

REM 检查部署结果
if %errorlevel% equ 0 (
    echo.
    echo ✅ 部署完成！
    echo 🌐 访问地址: https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/
    echo.
    echo 📊 查看函数状态:
    tcb functions:list
    echo.
    echo 📝 查看函数日志:
    echo    tcb functions:log portfolio-system-v14
    echo.
    echo 🔧 更新函数配置:
    echo    tcb functions:config:update portfolio-system-v14
) else (
    echo ❌ 部署失败，请检查错误信息
    cd ..
    rmdir /s /q temp_deploy
    del cloudbaserc.json >nul 2>nul
    pause
    exit /b 1
)

REM 清理临时文件
cd ..
rmdir /s /q temp_deploy
del cloudbaserc.json >nul 2>nul

echo 🧹 清理完成
echo 🎉 部署流程结束
pause
