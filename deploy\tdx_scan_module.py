#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通达信扫雷模块 (TDX Scan Module)
====================================

用于获取和计算股票的通达信扫雷风险评估分数

主要功能:
- 获取股票风险数据
- 计算扫雷分数 (已验证正确的算法)
- 风险等级评估
- 数据缓存机制
- 批量处理支持

使用示例:
    from tdx_scan_module import TdxScanClient
    
    # 初始化客户端
    scanner = TdxScanClient()
    
    # 获取单只股票分数
    result = scanner.get_stock_score('000001')
    if result:
        print(f"分数: {result['score']}, 风险等级: {result['risk_level']}")
    
    # 批量获取
    batch_result = scanner.get_batch_scores(['000001', '000002', '600036'])

作者: AI Assistant
版本: 1.0
日期: 2025-01-22
"""

import requests
import json
import time
import threading
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta


class TdxScanClient:
    """通达信扫雷客户端"""
    
    def __init__(self, cache_duration: int = 3600, request_timeout: int = 10):
        """
        初始化客户端
        
        Args:
            cache_duration: 缓存持续时间(秒), 默认1小时
            request_timeout: 请求超时时间(秒), 默认10秒
        """
        self.base_url = "http://page3.tdx.com.cn:7615/site/pcwebcall_static/bxb/json/"
        self.cache_duration = cache_duration
        self.request_timeout = request_timeout
        self._cache = {}
        self._cache_lock = threading.Lock()
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
        }
    
    def get_stock_score(self, stock_code: str) -> Optional[Dict]:
        """
        获取单只股票的扫雷分数
        
        Args:
            stock_code: 股票代码 (如: '000001', '600036')
            
        Returns:
            包含分数和风险信息的字典，失败返回None
            {
                'stock_code': '000001',
                'stock_name': '平安银行',
                'score': 97,
                'risk_level': '极低风险',
                'risk_color': '#10b068',
                'triggered_risks': 1,
                'total_risks': 40,
                'trigger_rate': 2.5,
                'details': [...],
                'update_time': '2025-01-22 10:30:00'
            }
        """
        # 检查缓存
        with self._cache_lock:
            if stock_code in self._cache:
                cache_data = self._cache[stock_code]
                if datetime.now() - cache_data['cache_time'] < timedelta(seconds=self.cache_duration):
                    return cache_data['data']
        
        try:
            # 获取原始数据
            raw_data = self._fetch_raw_data(stock_code)
            if not raw_data:
                return None
            
            # 计算分数
            result = self._calculate_score(stock_code, raw_data)
            
            # 缓存结果
            with self._cache_lock:
                self._cache[stock_code] = {
                    'data': result,
                    'cache_time': datetime.now()
                }
            
            return result
            
        except Exception as e:
            print(f"获取 {stock_code} 扫雷数据失败: {e}")
            return None
    
    def get_batch_scores(self, stock_codes: List[str], delay: float = 0.5) -> Dict[str, Dict]:
        """
        批量获取多只股票的扫雷分数
        
        Args:
            stock_codes: 股票代码列表
            delay: 请求间隔时间(秒), 避免频繁请求
            
        Returns:
            {股票代码: 分数数据} 的字典
        """
        results = {}
        
        for i, code in enumerate(stock_codes):
            try:
                result = self.get_stock_score(code)
                if result:
                    results[code] = result
                
                # 避免请求过于频繁
                if i < len(stock_codes) - 1:
                    time.sleep(delay)
                    
            except Exception as e:
                print(f"批量获取 {code} 失败: {e}")
                continue
        
        return results
    
    def _fetch_raw_data(self, stock_code: str) -> Optional[Dict]:
        """获取原始数据"""
        url = f"{self.base_url}{stock_code}.json"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"请求 {stock_code} 数据失败: {e}")
            return None
    
    def _calculate_score(self, stock_code: str, raw_data: Dict) -> Dict:
        """
        计算扫雷分数
        
        核心算法 (已验证):
        1. 初始分数 100分
        2. 只计算主风险项 (trig=1 且 fs>0)
        3. 不计算关联风险项 (commonlxid)
        4. 最终分数 = 100 - 触发风险的fs总和 (最低1分)
        """
        if not raw_data or 'data' not in raw_data:
            return self._create_error_result(stock_code, "数据格式错误")
        
        # 初始分数
        score = 100
        triggered_risks = 0
        total_risks = 0
        risk_details = []
        
        # 遍历所有风险类别
        for category in raw_data['data']:
            category_name = category.get('name', '未知类别')
            
            # 遍历风险项
            for risk_item in category.get('rows', []):
                total_risks += 1
                
                # 只计算主风险项 (关键算法)
                if risk_item.get('trig') == 1 and risk_item.get('fs', 0) > 0:
                    fs = risk_item.get('fs', 0)
                    triggered_risks += 1
                    score -= fs
                    
                    risk_details.append({
                        'category': category_name,
                        'name': risk_item.get('lx', '未知风险'),
                        'score': fs,
                        'reason': risk_item.get('trigyy', '')
                    })
        
        # 确保分数不小于1
        if score < 1:
            score = 1
        
        return {
            'stock_code': stock_code,
            'stock_name': raw_data.get('name', '未知股票'),
            'score': score,
            'risk_level': self._get_risk_level(score),
            'risk_color': self._get_risk_color(score),
            'triggered_risks': triggered_risks,
            'total_risks': total_risks,
            'trigger_rate': round(triggered_risks / total_risks * 100, 1) if total_risks > 0 else 0,
            'details': risk_details,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _create_error_result(self, stock_code: str, error_msg: str) -> Dict:
        """创建错误结果"""
        return {
            'stock_code': stock_code,
            'stock_name': '未知',
            'score': 0,
            'risk_level': '数据异常',
            'risk_color': '#cccccc',
            'triggered_risks': 0,
            'total_risks': 0,
            'trigger_rate': 0,
            'details': [],
            'error': error_msg,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _get_risk_level(self, score: float) -> str:
        """根据分数确定风险等级"""
        if score >= 90:
            return "极低风险"
        elif score >= 80:
            return "低风险"
        elif score >= 70:
            return "中低风险"
        elif score >= 60:
            return "中等风险"
        elif score >= 50:
            return "中高风险"
        elif score >= 40:
            return "高风险"
        else:
            return "极高风险"
    
    def _get_risk_color(self, score: float) -> str:
        """根据分数返回颜色代码"""
        if score >= 90:
            return "#10b068"  # 深绿色
        elif score >= 80:
            return "#52c41a"  # 绿色
        elif score >= 70:
            return "#faad14"  # 黄色
        elif score >= 60:
            return "#fa8c16"  # 橙色
        elif score >= 50:
            return "#fa541c"  # 深橙色
        elif score >= 40:
            return "#f5222d"  # 红色
        else:
            return "#a8071a"  # 深红色
    
    def format_display_text(self, result: Dict) -> str:
        """格式化显示文本"""
        if not result or 'error' in result:
            return "数据获取失败"
        
        return f"{result['score']}分 ({result['risk_level']}) 触发{result['triggered_risks']}项"
    
    def clear_cache(self):
        """清空缓存"""
        with self._cache_lock:
            self._cache.clear()
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        with self._cache_lock:
            return {
                'cached_stocks': len(self._cache),
                'cache_duration': self.cache_duration,
                'stocks': list(self._cache.keys())
            }


# 便捷函数
def get_stock_scan_score(stock_code: str) -> Optional[Dict]:
    """
    便捷函数：获取单只股票的扫雷分数
    
    Args:
        stock_code: 股票代码
        
    Returns:
        分数数据字典或None
    """
    client = TdxScanClient()
    return client.get_stock_score(stock_code)


def get_batch_scan_scores(stock_codes: List[str]) -> Dict[str, Dict]:
    """
    便捷函数：批量获取扫雷分数
    
    Args:
        stock_codes: 股票代码列表
        
    Returns:
        {股票代码: 分数数据} 字典
    """
    client = TdxScanClient()
    return client.get_batch_scores(stock_codes)


# 测试函数
def test_module():
    """测试模块功能"""
    print("=== 通达信扫雷模块测试 ===")
    
    # 测试单只股票
    print("\n1. 测试单只股票 (000001):")
    result = get_stock_scan_score('000001')
    if result:
        print(f"   {result['stock_name']}: {result['score']}分 ({result['risk_level']})")
        print(f"   触发风险: {result['triggered_risks']}项")
    
    # 测试批量获取
    print("\n2. 测试批量获取:")
    batch_results = get_batch_scan_scores(['000001', '000002', '600036'])
    for code, data in batch_results.items():
        if data:
            print(f"   {code} ({data['stock_name']}): {data['score']}分")
    
    print("\n测试完成!")


if __name__ == "__main__":
    test_module()
