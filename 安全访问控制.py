#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全访问控制模块
==============

为持仓系统添加简单的访问控制功能，保护你的数据安全。

使用方法：
1. 将此文件放在持仓系统同一目录
2. 在持仓系统_v14.py中导入并使用
3. 设置访问密码和允许的IP地址

作者: AI Assistant
版本: 1.0
日期: 2025-07-30
"""

from flask import request, session, redirect, url_for, render_template_string
from functools import wraps
import os
from datetime import datetime

class SecurityManager:
    """安全管理器"""
    
    def __init__(self, app):
        self.app = app
        self.app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')
        
        # 配置项
        self.access_password = os.environ.get('ACCESS_PASSWORD', '123456')
        self.allowed_ips = os.environ.get('ALLOWED_IPS', '').split(',') if os.environ.get('ALLOWED_IPS') else []
        self.enable_time_limit = os.environ.get('ENABLE_TIME_LIMIT', 'false').lower() == 'true'
        self.work_start_hour = int(os.environ.get('WORK_START_HOUR', '9'))
        self.work_end_hour = int(os.environ.get('WORK_END_HOUR', '18'))
        
        # 注册路由
        self.register_routes()
        
        # 注册请求前处理
        self.app.before_request(self.before_request)
    
    def register_routes(self):
        """注册安全相关路由"""
        
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            if request.method == 'POST':
                password = request.form.get('password')
                if password == self.access_password:
                    session['logged_in'] = True
                    session['login_time'] = datetime.now().isoformat()
                    return redirect('/')
                else:
                    return render_template_string(self.get_login_template(), error='密码错误，请重试')
            
            return render_template_string(self.get_login_template())
        
        @self.app.route('/logout')
        def logout():
            session.clear()
            return redirect(url_for('login'))
    
    def before_request(self):
        """请求前检查"""
        # 排除登录相关页面
        if request.endpoint in ['login', 'logout', 'static']:
            return
        
        # IP白名单检查
        if self.allowed_ips and request.remote_addr not in self.allowed_ips:
            return f'访问被拒绝：IP地址 {request.remote_addr} 不在允许列表中', 403
        
        # 时间限制检查
        if self.enable_time_limit:
            current_hour = datetime.now().hour
            if current_hour < self.work_start_hour or current_hour > self.work_end_hour:
                return f'访问时间限制：系统仅在 {self.work_start_hour}:00-{self.work_end_hour}:00 开放', 403
        
        # 登录检查
        if 'logged_in' not in session:
            return redirect(url_for('login'))
    
    def get_login_template(self):
        """获取登录页面模板"""
        return '''
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>持仓系统 - 登录</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    height: 100vh;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                .login-container {
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                    width: 100%;
                    max-width: 400px;
                    text-align: center;
                }
                .login-title {
                    color: #333;
                    margin-bottom: 30px;
                    font-size: 24px;
                    font-weight: 300;
                }
                .form-group {
                    margin-bottom: 20px;
                    text-align: left;
                }
                .form-label {
                    display: block;
                    margin-bottom: 5px;
                    color: #555;
                    font-weight: 500;
                }
                .form-input {
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-size: 16px;
                    transition: border-color 0.3s;
                    box-sizing: border-box;
                }
                .form-input:focus {
                    outline: none;
                    border-color: #667eea;
                }
                .login-button {
                    width: 100%;
                    padding: 12px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    border-radius: 5px;
                    font-size: 16px;
                    cursor: pointer;
                    transition: transform 0.2s;
                }
                .login-button:hover {
                    transform: translateY(-2px);
                }
                .error-message {
                    color: #e74c3c;
                    margin-top: 15px;
                    padding: 10px;
                    background: #fdf2f2;
                    border-radius: 5px;
                    border-left: 4px solid #e74c3c;
                }
                .info-text {
                    color: #666;
                    font-size: 14px;
                    margin-top: 20px;
                    line-height: 1.5;
                }
            </style>
        </head>
        <body>
            <div class="login-container">
                <h2 class="login-title">🏦 持仓系统</h2>
                <form method="post">
                    <div class="form-group">
                        <label class="form-label" for="password">访问密码</label>
                        <input type="password" id="password" name="password" class="form-input" required placeholder="请输入访问密码">
                    </div>
                    <button type="submit" class="login-button">🔓 登录系统</button>
                </form>
                {% if error %}
                <div class="error-message">{{ error }}</div>
                {% endif %}
                <div class="info-text">
                    <p>🔒 此系统受密码保护</p>
                    <p>📱 支持手机和电脑访问</p>
                    <p>⏰ 当前时间：{{ datetime.now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
                </div>
            </div>
        </body>
        </html>
        '''

def init_security(app, **config):
    """初始化安全控制
    
    参数：
    - access_password: 访问密码
    - allowed_ips: 允许的IP地址列表
    - enable_time_limit: 是否启用时间限制
    - work_start_hour: 工作开始时间
    - work_end_hour: 工作结束时间
    """
    
    # 设置环境变量
    for key, value in config.items():
        if key.upper() not in os.environ:
            os.environ[key.upper()] = str(value)
    
    # 创建安全管理器
    security_manager = SecurityManager(app)
    
    print("🔒 安全控制已启用")
    print(f"   密码保护: {'✅' if security_manager.access_password != '123456' else '⚠️ 使用默认密码'}")
    print(f"   IP限制: {'✅' if security_manager.allowed_ips else '❌'}")
    print(f"   时间限制: {'✅' if security_manager.enable_time_limit else '❌'}")
    
    return security_manager

# 使用示例
if __name__ == '__main__':
    from flask import Flask
    
    app = Flask(__name__)
    
    # 初始化安全控制
    init_security(app, 
                 access_password='your_password_here',  # 设置你的密码
                 allowed_ips=['127.0.0.1'],            # 允许的IP地址
                 enable_time_limit=False,               # 是否启用时间限制
                 work_start_hour=9,                     # 工作开始时间
                 work_end_hour=18)                      # 工作结束时间
    
    @app.route('/')
    def index():
        return '<h1>欢迎访问持仓系统！</h1><p><a href="/logout">退出登录</a></p>'
    
    app.run(debug=True)
