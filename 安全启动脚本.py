#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统安全启动脚本
==================

集成安全控制功能的启动脚本，保护你的持仓数据。

功能特性：
- 密码保护
- IP白名单
- 时间访问限制
- 会话管理

使用方法：
python 安全启动脚本.py

作者: AI Assistant
版本: 1.0
日期: 2025-07-30
"""

import os
import sys
from datetime import datetime

def setup_security_config():
    """设置安全配置"""
    print("🔧 配置安全设置...")
    
    # 获取用户输入的安全配置
    print("\n请设置访问控制参数（直接回车使用默认值）：")
    
    # 访问密码
    password = input("设置访问密码 [默认: 123456]: ").strip()
    if not password:
        password = "123456"
    os.environ['ACCESS_PASSWORD'] = password
    
    # IP白名单
    print("\nIP白名单设置（可选）：")
    print("示例：127.0.0.1,*************")
    allowed_ips = input("允许访问的IP地址（用逗号分隔，留空表示不限制）: ").strip()
    if allowed_ips:
        os.environ['ALLOWED_IPS'] = allowed_ips
    
    # 时间限制
    enable_time = input("是否启用时间访问限制？[y/N]: ").strip().lower()
    if enable_time == 'y':
        os.environ['ENABLE_TIME_LIMIT'] = 'true'
        start_hour = input("工作开始时间（小时，0-23）[默认: 9]: ").strip()
        end_hour = input("工作结束时间（小时，0-23）[默认: 18]: ").strip()
        
        if start_hour.isdigit():
            os.environ['WORK_START_HOUR'] = start_hour
        if end_hour.isdigit():
            os.environ['WORK_END_HOUR'] = end_hour
    
    # 设置密钥
    os.environ['SECRET_KEY'] = f"stock-system-{datetime.now().strftime('%Y%m%d')}"
    
    print("\n✅ 安全配置完成！")

def start_secure_system():
    """启动安全的持仓系统"""
    try:
        print("🚀 启动安全持仓系统...")
        
        # 导入主系统
        from 持仓系统_v14 import app
        
        # 导入安全控制
        from 安全访问控制 import init_security
        
        # 初始化安全控制
        security_config = {}
        if 'ACCESS_PASSWORD' in os.environ:
            security_config['access_password'] = os.environ['ACCESS_PASSWORD']
        if 'ALLOWED_IPS' in os.environ:
            security_config['allowed_ips'] = os.environ['ALLOWED_IPS'].split(',')
        if 'ENABLE_TIME_LIMIT' in os.environ:
            security_config['enable_time_limit'] = os.environ['ENABLE_TIME_LIMIT'] == 'true'
        if 'WORK_START_HOUR' in os.environ:
            security_config['work_start_hour'] = int(os.environ['WORK_START_HOUR'])
        if 'WORK_END_HOUR' in os.environ:
            security_config['work_end_hour'] = int(os.environ['WORK_END_HOUR'])
        
        init_security(app, **security_config)
        
        # 获取端口
        port = int(os.environ.get('PORT', 5000))
        
        print(f"\n🌐 系统启动成功！")
        print(f"   本地访问：http://localhost:{port}")
        print(f"   网络访问：http://0.0.0.0:{port}")
        
        if 'ACCESS_PASSWORD' in os.environ:
            print(f"   访问密码：{os.environ['ACCESS_PASSWORD']}")
        
        if 'ALLOWED_IPS' in os.environ:
            print(f"   IP限制：{os.environ['ALLOWED_IPS']}")
        
        if os.environ.get('ENABLE_TIME_LIMIT') == 'true':
            start_hour = os.environ.get('WORK_START_HOUR', '9')
            end_hour = os.environ.get('WORK_END_HOUR', '18')
            print(f"   时间限制：{start_hour}:00 - {end_hour}:00")
        
        print(f"\n💡 提示：")
        print(f"   - 首次访问需要输入密码")
        print(f"   - 访问 /logout 可以退出登录")
        print(f"   - 按 Ctrl+C 停止服务")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保以下文件在同一目录：")
        print("  - 持仓系统_v14.py")
        print("  - 安全访问控制.py")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    print("=" * 50)
    print("🏦 持仓系统安全启动器")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ['持仓系统_v14.py', '安全访问控制.py']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件：")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都在同一目录中。")
        sys.exit(1)
    
    # 设置安全配置
    setup_security_config()
    
    # 启动系统
    start_secure_system()

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 系统已停止，感谢使用！")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
