#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署前检查脚本
检查所有必需的文件和配置是否就绪
"""

import os
import json
import sys

def check_file_exists(filename, required=True):
    """检查文件是否存在"""
    exists = os.path.exists(filename)
    status = "✅" if exists else ("❌" if required else "⚠️")
    print(f"{status} {filename}")
    return exists

def check_json_file(filename):
    """检查JSON文件格式"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"✅ {filename} - JSON格式正确")
        return True
    except Exception as e:
        print(f"❌ {filename} - JSON格式错误: {e}")
        return False

def main():
    print("🔍 部署前检查...")
    print("=" * 50)
    
    all_good = True
    
    # 检查核心文件
    print("\n📁 核心文件检查:")
    core_files = [
        "portfolio_system_v14.py",
        "requirements.txt", 
        "cloudbaserc.json"
    ]
    
    for file in core_files:
        if not check_file_exists(file):
            all_good = False
    
    # 检查依赖模块
    print("\n🔧 依赖模块检查:")
    module_files = [
        "A股交易时间监测_简化版.py",
        "yearly_low_cache_reader.py", 
        "tdx_scan_module.py"
    ]
    
    for file in module_files:
        if not check_file_exists(file):
            all_good = False
    
    # 检查数据文件
    print("\n💾 数据文件检查:")
    data_files = [
        ("stock_data_cache.json", True),
        ("imported_stock_list.json", False),
        ("uploads", False)
    ]
    
    for file, required in data_files:
        if not check_file_exists(file, required):
            if required:
                all_good = False
    
    # 检查JSON文件格式
    print("\n📋 配置文件格式检查:")
    json_files = ["cloudbaserc.json"]
    
    if os.path.exists("stock_data_cache.json"):
        json_files.append("stock_data_cache.json")
    
    for file in json_files:
        if os.path.exists(file):
            if not check_json_file(file):
                all_good = False
    
    # 检查Python语法
    print("\n🐍 Python语法检查:")
    try:
        import py_compile
        py_compile.compile("portfolio_system_v14.py", doraise=True)
        print("✅ portfolio_system_v14.py - 语法正确")
    except Exception as e:
        print(f"❌ portfolio_system_v14.py - 语法错误: {e}")
        all_good = False
    
    # 总结
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 所有检查通过，可以开始部署！")
        print("\n📝 部署命令:")
        print("   Windows: deploy.bat")
        print("   Linux/Mac: chmod +x deploy_cloudbase.sh && ./deploy_cloudbase.sh")
        return 0
    else:
        print("❌ 检查发现问题，请修复后再部署")
        return 1

if __name__ == "__main__":
    sys.exit(main())
