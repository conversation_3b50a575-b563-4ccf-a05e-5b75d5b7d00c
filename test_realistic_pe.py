#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实回本年数计算逻辑
"""

def test_realistic_pe_calculation():
    """测试不同情况下的真实回本年数计算"""
    
    print("🧪 测试真实回本年数计算逻辑")
    print("=" * 60)
    
    # 测试案例1: 全部盈利股票
    print("\n📊 案例1: 全部盈利股票")
    print("-" * 40)
    stocks_case1 = [
        {'name': '股票A', 'pe_ratio': 10, 'market_value': 100000},
        {'name': '股票B', 'pe_ratio': 20, 'market_value': 200000},
        {'name': '股票C', 'pe_ratio': 15, 'market_value': 300000},
    ]
    result1 = calculate_realistic_pe(stocks_case1)
    print(f"盈利股票加权PE: {result1['profitable_pe']:.2f}年")
    print(f"真实回本年数: {result1['realistic_payback']:.2f}年")
    print(f"亏损股票占比: {result1['loss_ratio']:.1f}%")
    
    # 测试案例2: 包含亏损股票
    print("\n📊 案例2: 包含亏损股票")
    print("-" * 40)
    stocks_case2 = [
        {'name': '股票A', 'pe_ratio': 10, 'market_value': 100000},   # 盈利
        {'name': '股票B', 'pe_ratio': -5, 'market_value': 200000},   # 亏损
        {'name': '股票C', 'pe_ratio': 20, 'market_value': 300000},   # 盈利
    ]
    result2 = calculate_realistic_pe(stocks_case2)
    print(f"盈利股票加权PE: {result2['profitable_pe']:.2f}年")
    print(f"真实回本年数: {result2['realistic_payback']:.2f}年")
    print(f"亏损股票占比: {result2['loss_ratio']:.1f}%")
    
    # 测试案例3: 大部分亏损
    print("\n📊 案例3: 大部分亏损股票")
    print("-" * 40)
    stocks_case3 = [
        {'name': '股票A', 'pe_ratio': 15, 'market_value': 100000},   # 盈利
        {'name': '股票B', 'pe_ratio': -10, 'market_value': 300000},  # 亏损
        {'name': '股票C', 'pe_ratio': -8, 'market_value': 200000},   # 亏损
    ]
    result3 = calculate_realistic_pe(stocks_case3)
    print(f"盈利股票加权PE: {result3['profitable_pe']:.2f}年")
    print(f"真实回本年数: {result3['realistic_payback']:.2f}年")
    print(f"亏损股票占比: {result3['loss_ratio']:.1f}%")
    
    # 测试案例4: 全部亏损
    print("\n📊 案例4: 全部亏损股票")
    print("-" * 40)
    stocks_case4 = [
        {'name': '股票A', 'pe_ratio': -5, 'market_value': 100000},
        {'name': '股票B', 'pe_ratio': -10, 'market_value': 200000},
        {'name': '股票C', 'pe_ratio': -15, 'market_value': 300000},
    ]
    result4 = calculate_realistic_pe(stocks_case4)
    print(f"盈利股票加权PE: {result4['profitable_pe']:.2f}年")
    print(f"真实回本年数: {result4['realistic_payback']:.2f}年")
    print(f"亏损股票占比: {result4['loss_ratio']:.1f}%")
    
    print("\n" + "=" * 60)
    print("💡 结论:")
    print("1. 盈利股票加权PE: 只考虑盈利股票的回本年数")
    print("2. 真实回本年数: 考虑亏损股票影响的实际回本时间")
    print("3. 亏损股票按999年处理，真实反映投资风险")

def calculate_realistic_pe(stocks):
    """计算真实回本年数"""
    total_market_value = sum(s['market_value'] for s in stocks)
    
    # 分别计算盈利和亏损股票
    profitable_pe_sum = 0
    profitable_market_value = 0
    loss_making_market_value = 0
    
    for stock in stocks:
        pe_ratio = stock['pe_ratio']
        market_value = stock['market_value']
        
        if pe_ratio > 0:
            # 盈利股票
            profitable_pe_sum += pe_ratio * market_value
            profitable_market_value += market_value
        else:
            # 亏损股票
            loss_making_market_value += market_value
    
    # 计算指标
    profitable_pe = (profitable_pe_sum / profitable_market_value) if profitable_market_value > 0 else 0
    loss_ratio = (loss_making_market_value / total_market_value * 100) if total_market_value > 0 else 0
    
    # 真实回本年数
    if total_market_value > 0:
        profitable_ratio = profitable_market_value / total_market_value
        loss_ratio_decimal = loss_making_market_value / total_market_value
        realistic_payback = profitable_pe * profitable_ratio + 999 * loss_ratio_decimal
    else:
        realistic_payback = 0
    
    return {
        'profitable_pe': profitable_pe,
        'realistic_payback': realistic_payback,
        'loss_ratio': loss_ratio
    }

if __name__ == "__main__":
    test_realistic_pe_calculation()
