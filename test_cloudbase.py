#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CloudBase部署测试文件
==================

用于测试腾讯云CloudBase部署是否正常工作

Author: AI Assistant
Date: 2025-07-30
"""

from flask import Flask, jsonify
import os
import json

app = Flask(__name__)

@app.route('/')
def index():
    """测试首页"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>持仓系统 CloudBase 测试</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { padding: 20px; border-radius: 5px; margin: 10px 0; }
            .success { background-color: #d4edda; color: #155724; }
            .info { background-color: #d1ecf1; color: #0c5460; }
        </style>
    </head>
    <body>
        <h1>🚀 持仓系统 CloudBase 测试页面</h1>
        <div class="status success">
            ✅ CloudBase 部署成功！
        </div>
        <div class="status info">
            📊 这是持仓系统V14的CloudBase兼容版本测试页面
        </div>
        <h2>功能测试</h2>
        <ul>
            <li><a href="/api/test">API测试</a></li>
            <li><a href="/api/env">环境变量测试</a></li>
            <li><a href="/api/status">系统状态</a></li>
        </ul>
        <h2>部署信息</h2>
        <p>版本: V14-CloudBase</p>
        <p>运行环境: 腾讯云CloudBase</p>
        <p>部署时间: 2025-07-30</p>
    </body>
    </html>
    """

@app.route('/api/test')
def api_test():
    """API功能测试"""
    return jsonify({
        'success': True,
        'message': 'CloudBase API测试成功',
        'version': 'V14-CloudBase',
        'timestamp': '2025-07-30'
    })

@app.route('/api/env')
def env_test():
    """环境变量测试"""
    env_info = {
        'TENCENTCLOUD_RUNENV': os.environ.get('TENCENTCLOUD_RUNENV', 'Not Set'),
        'FLASK_ENV': os.environ.get('FLASK_ENV', 'Not Set'),
        'WECHAT_WEBHOOK_URL': '已配置' if os.environ.get('WECHAT_WEBHOOK_URL') else '未配置',
        'PORT': os.environ.get('PORT', 'Not Set'),
        'is_cloudbase': os.environ.get('TENCENTCLOUD_RUNENV') == 'SCF'
    }
    
    return jsonify({
        'success': True,
        'environment': env_info
    })

@app.route('/api/status')
def status():
    """系统状态检查"""
    try:
        # 尝试导入主应用
        try:
            from 持仓系统_v14 import app as main_app
            main_app_status = '✅ 主应用导入成功'
        except ImportError as e:
            main_app_status = f'❌ 主应用导入失败: {str(e)}'
        
        status_info = {
            'cloudbase_test': '✅ CloudBase测试应用运行正常',
            'main_app_import': main_app_status,
            'python_version': f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            'flask_version': 'Flask已安装'
        }
        
        return jsonify({
            'success': True,
            'status': status_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# CloudBase入口函数
def main_handler(event, context):
    """CloudBase云函数入口函数"""
    try:
        import io
        from werkzeug.serving import WSGIRequestHandler
        from werkzeug.wrappers import Request
        
        # 构造WSGI环境
        method = event.get('httpMethod', 'GET')
        path = event.get('path', '/')
        query_string = event.get('queryString', '')
        headers = event.get('headers', {})
        body = event.get('body', '')
        
        # 如果body是base64编码的，需要解码
        if event.get('isBase64Encoded', False):
            import base64
            body = base64.b64decode(body).decode('utf-8')
        
        # 创建WSGI环境
        environ = {
            'REQUEST_METHOD': method,
            'PATH_INFO': path,
            'QUERY_STRING': query_string,
            'CONTENT_TYPE': headers.get('content-type', ''),
            'CONTENT_LENGTH': str(len(body.encode('utf-8'))),
            'wsgi.input': io.BytesIO(body.encode('utf-8')),
            'wsgi.errors': io.StringIO(),
            'wsgi.version': (1, 0),
            'wsgi.multithread': False,
            'wsgi.multiprocess': True,
            'wsgi.run_once': False,
            'wsgi.url_scheme': 'https',
            'SERVER_NAME': headers.get('host', 'localhost').split(':')[0],
            'SERVER_PORT': '443',
            'HTTP_HOST': headers.get('host', 'localhost'),
        }
        
        # 添加其他HTTP头
        for key, value in headers.items():
            key = 'HTTP_' + key.upper().replace('-', '_')
            if key not in environ:
                environ[key] = value
        
        # 调用Flask应用
        response_data = []
        status_code = '200'
        response_headers = []
        
        def start_response(status, headers):
            nonlocal status_code, response_headers
            status_code = status.split(' ')[0]
            response_headers = headers
            
        response = app(environ, start_response)
        
        # 收集响应数据
        for data in response:
            if isinstance(data, bytes):
                response_data.append(data.decode('utf-8'))
            else:
                response_data.append(str(data))
        
        response_body = ''.join(response_data)
        
        # 构造响应头字典
        headers_dict = {}
        for header_name, header_value in response_headers:
            headers_dict[header_name] = header_value
            
        return {
            'statusCode': int(status_code),
            'headers': headers_dict,
            'body': response_body
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({'error': str(e), 'message': 'CloudBase测试应用错误'})
        }

if __name__ == '__main__':
    print("🚀 启动CloudBase测试应用...")
    port = int(os.environ.get('PORT', 5000))
    print(f"🌐 测试地址: http://localhost:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
