#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统云环境启动脚本
===================

专门为云部署优化的启动脚本，处理云环境的特殊需求。

作者: AI Assistant
版本: 1.0
日期: 2025-07-30
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_cloud_environment():
    """设置云环境"""
    logger.info("🔧 设置云环境...")
    
    # 设置环境变量
    os.environ.setdefault('FLASK_ENV', 'production')
    
    # 创建必要的目录
    directories = ['uploads', 'cache', 'logs']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info(f"📁 创建目录: {directory}")
    
    logger.info("✅ 云环境设置完成")

def main():
    """主函数"""
    logger.info("🚀 启动持仓系统云版本...")
    
    # 设置云环境
    setup_cloud_environment()
    
    try:
        # 导入主应用
        from 持仓系统_v14 import app
        
        # 获取端口
        port = int(os.environ.get('PORT', 5000))
        
        logger.info(f"🌐 启动服务，端口: {port}")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=port,
            debug=False,
            threaded=True  # 支持多线程
        )
        
    except Exception as e:
        logger.error(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
