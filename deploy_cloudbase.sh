#!/bin/bash

echo "🚀 开始部署持仓系统到腾讯云CloudBase..."
echo ""

# 检查是否安装了CloudBase CLI
if ! command -v tcb &> /dev/null; then
    echo "❌ 未找到腾讯云CLI工具，请先安装："
    echo "   npm install -g @cloudbase/cli"
    exit 1
fi

# 检查是否已登录
echo "📝 检查登录状态..."
if ! tcb auth:list &> /dev/null; then
    echo "🔑 请先登录腾讯云："
    tcb login
fi

# 部署函数
echo "📦 开始部署云函数..."
tcb functions:deploy portfolio-system

# 检查部署结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 部署完成！"
    echo "🌐 访问地址: https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/"
    echo ""
    echo "📊 查看函数状态:"
    tcb functions:list
else
    echo "❌ 部署失败，请检查错误信息"
    exit 1
fi
