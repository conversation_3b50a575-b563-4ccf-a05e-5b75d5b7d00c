<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市值加权指标 - 持仓系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .metric-card.dividend {
            border-left-color: #e74c3c;
        }

        .metric-card.pb {
            border-left-color: #3498db;
        }

        .metric-card.pe {
            border-left-color: #2ecc71;
        }

        .metric-title {
            font-size: 1.1em;
            color: #34495e;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .metric-card.dividend .metric-value {
            color: #e74c3c;
        }

        .metric-card.pb .metric-value {
            color: #3498db;
        }

        .metric-card.pe .metric-value {
            color: #2ecc71;
        }

        .metric-unit {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-left: 5px;
        }

        .metric-info {
            color: #7f8c8d;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .summary-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }

        .summary-title {
            font-size: 1.3em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .summary-item .label {
            color: #7f8c8d;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .summary-item .value {
            color: #2c3e50;
            font-size: 1.4em;
            font-weight: 600;
        }

        .refresh-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px auto;
            display: block;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .loading {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }

        .update-time {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-value {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 市值加权指标</h1>
            <p>基于持仓市值计算的加权平均指标</p>
        </div>

        <div id="loading" class="loading">
            正在加载数据...
        </div>

        <div id="error" class="error" style="display: none;"></div>

        <div id="content" style="display: none;">
            <div class="summary-section">
                <div class="summary-title">📈 投资组合概览</div>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="label">总市值</div>
                        <div class="value" id="totalMarketValue">-</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">股票数量</div>
                        <div class="value" id="stockCount">-</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">有股息股票</div>
                        <div class="value" id="validDividendStocks">-</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">有PB数据股票</div>
                        <div class="value" id="validPbStocks">-</div>
                    </div>
                    <div class="summary-item">
                        <div class="label">有PE数据股票</div>
                        <div class="value" id="validPeStocks">-</div>
                    </div>
                </div>
            </div>

            <div class="metrics-grid">
                <div class="metric-card dividend">
                    <div class="metric-title">💰 市值加权股息率TTM</div>
                    <div class="metric-value">
                        <span id="dividendYield">-</span>
                        <span class="metric-unit">%</span>
                    </div>
                    <div class="metric-info">
                        按持仓市值加权计算的年化股息收益率，反映整体投资组合的分红回报水平
                    </div>
                </div>

                <div class="metric-card pb">
                    <div class="metric-title">📊 市值加权市净率</div>
                    <div class="metric-value">
                        <span id="pbRatio">-</span>
                        <span class="metric-unit">倍</span>
                    </div>
                    <div class="metric-info">
                        按持仓市值加权计算的市净率，反映整体投资组合相对于净资产的估值水平
                    </div>
                </div>

                <div class="metric-card pe">
                    <div class="metric-title">⚡ 市值加权市盈率TTM</div>
                    <div class="metric-value">
                        <span id="peRatio">-</span>
                        <span class="metric-unit">倍</span>
                    </div>
                    <div class="metric-info">
                        按持仓市值加权计算的TTM市盈率，反映整体投资组合相对于盈利的估值水平
                    </div>
                </div>
            </div>

            <button class="refresh-btn" onclick="loadMetrics()">🔄 刷新数据</button>
            
            <div class="update-time" id="updateTime"></div>
        </div>
    </div>

    <script>
        async function loadMetrics() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('content');

            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';

            try {
                const response = await fetch('/api/market-weighted-metrics');
                const result = await response.json();

                if (result.success) {
                    const data = result.data;
                    
                    // 更新指标值
                    document.getElementById('dividendYield').textContent = data.market_weighted_dividend_yield_ttm || '0.00';
                    document.getElementById('pbRatio').textContent = data.market_weighted_pb_ratio || '0.00';
                    document.getElementById('peRatio').textContent = data.market_weighted_pe_ttm || '0.00';
                    
                    // 更新概览数据
                    document.getElementById('totalMarketValue').textContent = formatNumber(data.total_market_value) + '元';
                    document.getElementById('stockCount').textContent = data.stock_count + '只';
                    document.getElementById('validDividendStocks').textContent = data.valid_dividend_stocks + '只';
                    document.getElementById('validPbStocks').textContent = data.valid_pb_stocks + '只';
                    document.getElementById('validPeStocks').textContent = data.valid_pe_stocks + '只';
                    
                    // 更新时间
                    document.getElementById('updateTime').textContent = '最后更新: ' + data.update_time;
                    
                    loading.style.display = 'none';
                    content.style.display = 'block';
                } else {
                    throw new Error(result.message || '获取数据失败');
                }
            } catch (err) {
                loading.style.display = 'none';
                error.style.display = 'block';
                error.textContent = '加载失败: ' + err.message;
            }
        }

        function formatNumber(num) {
            if (num >= 100000000) {
                return (num / 100000000).toFixed(2) + '亿';
            } else if (num >= 10000) {
                return (num / 10000).toFixed(2) + '万';
            } else {
                return num.toFixed(2);
            }
        }

        // 页面加载时自动获取数据
        document.addEventListener('DOMContentLoaded', loadMetrics);
        
        // 每30秒自动刷新一次
        setInterval(loadMetrics, 30000);
    </script>
</body>
</html>
