@echo off
echo 🚀 开始部署持仓系统到腾讯云...
echo.

REM 检查是否安装了CloudBase CLI
where tcb >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 未找到腾讯云CLI工具，请先安装：
    echo    npm install -g @cloudbase/cli
    pause
    exit /b 1
)

REM 登录腾讯云（如果未登录）
echo 📝 检查登录状态...
tcb login --key-file ./cloudbase-key.json

REM 部署函数
echo 📦 开始部署云函数...
tcb functions:deploy portfolio-system

REM 部署静态资源（如果有）
if exist "static" (
    echo 🌐 部署静态资源...
    tcb hosting:deploy static -e portfolio-system-9g8w6qhj8b8e8c8a
)

echo.
echo ✅ 部署完成！
echo 🌐 访问地址: https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/portfolio-system
echo.
pause
