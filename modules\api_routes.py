#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API路由模块
===========

负责处理所有的Flask API路由，包括：
- 股票数据API
- 策略管理API
- 文件上传API
- 系统状态API

作者: AI Assistant
版本: 1.0
"""

from flask import jsonify, request, render_template_string
import json
import os
import pandas as pd
from werkzeug.utils import secure_filename
from datetime import datetime
from typing import Dict, Any


class APIResponseHandler:
    """API响应处理器 - 统一处理API响应格式和错误处理"""

    @staticmethod
    def success(data=None, message="操作成功"):
        """成功响应"""
        response = {
            'success': True,
            'message': message
        }
        if data is not None:
            response['data'] = data
        return jsonify(response)

    @staticmethod
    def error(message="操作失败", code=400):
        """错误响应"""
        return jsonify({
            'success': False,
            'message': message
        }), code

    @staticmethod
    def handle_exception(func):
        """异常处理装饰器"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                print(f"API异常 {func.__name__}: {e}")
                return APIResponseHandler.error(f"操作失败: {str(e)}")
        wrapper.__name__ = func.__name__
        return wrapper


class DataCleaner:
    """数据清理器 - 确保数据可以JSON序列化"""

    @staticmethod
    def clean_stock_data(stock_data_dict):
        """清理股票数据，确保JSON可序列化"""
        clean_data = []
        for code, stock_info in stock_data_dict.items():
            clean_stock = {}
            for key, value in stock_info.items():
                if value is None or value == '' or (isinstance(value, float) and str(value) == 'nan'):
                    clean_stock[key] = None
                else:
                    clean_stock[key] = value
            clean_data.append(clean_stock)
        return clean_data


def setup_routes(app, stock_data, strategy_manager, priority_manager, trading_monitor, 
                 scan_client, wechat_alert, cache_manager, data_persistence):
    """设置所有API路由"""
    
    # 文件上传配置
    ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
    
    def allowed_file(filename):
        """检查文件扩展名是否允许"""
        return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

    @app.route('/')
    def index():
        """主页面"""
        # 读取HTML模板
        template_path = os.path.join('templates', 'index.html')
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                html_template = f.read()
            return render_template_string(html_template)
        except FileNotFoundError:
            return "模板文件未找到", 404

    @app.route('/api/stocks')
    @APIResponseHandler.handle_exception
    def get_stocks():
        """获取股票数据API"""
        # 计算统计数据
        stats = calculate_statistics(stock_data)
        
        # 清理股票数据，确保JSON可序列化
        clean_data = DataCleaner.clean_stock_data(stock_data)
        
        return APIResponseHandler.success({
            'stocks': clean_data,
            'last_update_time': data_persistence.get_last_update_time(),
            'stats': stats
        })

    @app.route('/api/trading-status')
    @APIResponseHandler.handle_exception
    def get_trading_status():
        """获取交易状态API"""
        status = trading_monitor.get_trading_status()
        return APIResponseHandler.success(status)

    @app.route('/api/refresh-holidays', methods=['POST'])
    @APIResponseHandler.handle_exception
    def refresh_holidays():
        """刷新节假日数据API"""
        trading_monitor.refresh_holidays()
        return APIResponseHandler.success(message='节假日数据已刷新')

    @app.route('/api/clear-data', methods=['POST'])
    @APIResponseHandler.handle_exception
    def clear_data():
        """清空数据API"""
        stock_data.clear()
        data_persistence.save_stock_data(stock_data, None)
        return APIResponseHandler.success(message='数据已清空')

    @app.route('/api/upload-holdings', methods=['POST'])
    def upload_holdings():
        """上传持仓表格API"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'message': '没有选择文件'
                }), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'message': '没有选择文件'
                }), 400

            if file and allowed_file(file.filename):
                # 保存文件
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{timestamp}_{filename}"
                
                upload_folder = 'uploads'
                os.makedirs(upload_folder, exist_ok=True)
                filepath = os.path.join(upload_folder, filename)
                file.save(filepath)

                # 读取Excel文件
                df = pd.read_excel(filepath)
                
                # 处理股票列表
                imported_stocks = []
                for _, row in df.iterrows():
                    # 尝试不同的列名
                    code = None
                    name = None
                    holdings = 0
                    
                    # 查找股票代码列
                    for col in df.columns:
                        if '代码' in str(col) or 'code' in str(col).lower():
                            code = str(row[col]).strip()
                            break
                    
                    # 查找股票名称列
                    for col in df.columns:
                        if '名称' in str(col) or 'name' in str(col).lower():
                            name = str(row[col]).strip()
                            break
                    
                    # 查找持仓数量列
                    for col in df.columns:
                        if '数量' in str(col) or '持仓' in str(col) or 'holdings' in str(col).lower():
                            try:
                                holdings = float(row[col])
                            except:
                                holdings = 0
                            break
                    
                    if code and code != 'nan':
                        # 确保代码是6位数字
                        code = code.zfill(6)
                        if code.isdigit() and len(code) == 6:
                            imported_stocks.append({
                                '代码': code,
                                '名称': name or '',
                                '持仓数量': holdings
                            })

                # 保存导入的股票列表
                data_persistence.save_imported_list(imported_stocks)
                
                return jsonify({
                    'success': True,
                    'message': f'成功导入 {len(imported_stocks)} 只股票',
                    'data': {
                        'imported_count': len(imported_stocks),
                        'filename': filename
                    }
                })

            else:
                return jsonify({
                    'success': False,
                    'message': '不支持的文件格式，请上传Excel文件'
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'上传失败: {str(e)}'
            }), 500

    @app.route('/api/refresh-data', methods=['POST'])
    def refresh_data():
        """重新获取数据API"""
        try:
            imported_stock_list = data_persistence.load_imported_list()
            if not imported_stock_list:
                return jsonify({
                    'success': False,
                    'message': '没有导入的股票列表，请先上传持仓表格'
                }), 400

            # 这里需要调用数据更新逻辑
            # update_all_stocks() 需要在主应用中实现
            
            return jsonify({
                'success': True,
                'message': '数据更新完成'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新失败: {str(e)}'
            }), 500

    @app.route('/api/strategies', methods=['GET'])
    def get_strategies():
        """获取所有策略配置"""
        try:
            return jsonify({
                'success': True,
                'data': strategy_manager.strategies
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取策略配置失败: {str(e)}'
            }), 500

    @app.route('/api/strategies', methods=['POST'])
    def update_strategies():
        """更新策略配置"""
        try:
            data = request.get_json()
            
            print(f"📝 收到策略配置更新: {data}")
            
            # 更新策略配置
            for strategy_id, strategy_config in data.items():
                if strategy_id in strategy_manager.strategies:
                    strategy_manager.strategies[strategy_id].update(strategy_config)
                    print(f"  ✅ 更新策略: {strategy_id}")
                else:
                    print(f"  ⚠️ 策略不存在: {strategy_id}")
            
            # 保存配置
            strategy_manager.save_strategy_config()
            
            # 重新计算所有股票的卖出信号
            from sell_signal import recalculate_all_sell_signals
            recalculate_all_sell_signals(stock_data, strategy_manager, priority_manager)
            
            return jsonify({
                'success': True,
                'message': '策略配置更新成功'
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新策略配置失败: {str(e)}'
            }), 500

    @app.route('/api/auto-update', methods=['GET'])
    def get_auto_update_status():
        """获取自动更新状态"""
        try:
            return jsonify({
                'success': True,
                'data': {
                    'auto_update_enabled': data_persistence.get_auto_update_enabled()
                }
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取自动更新状态失败: {str(e)}'
            }), 500

    @app.route('/api/auto-update', methods=['POST'])
    def set_auto_update_status():
        """设置自动更新状态"""
        try:
            data = request.get_json()
            enabled = data.get('enabled', False)
            
            data_persistence.set_auto_update_enabled(enabled)
            
            return jsonify({
                'success': True,
                'message': f'自动更新已{"启用" if enabled else "禁用"}'
            })
            
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'设置自动更新状态失败: {str(e)}'
            }), 500


def calculate_statistics(stock_data: Dict) -> Dict[str, Any]:
    """计算统计数据"""
    if not stock_data:
        return {
            'total_market_value': 0,
            'stock_count': 0,
            'industry_distribution': {}
        }

    total_market_value = 0
    stock_count = 0
    industry_distribution = {}

    for code, stock_info in stock_data.items():
        # 只统计股票类型
        if stock_info.get('security_type') == '股票':
            stock_count += 1
            
            # 计算市值
            price = stock_info.get('price', 0)
            holdings = stock_info.get('持仓数量', 0)
            if price and holdings:
                market_value = price * holdings
                total_market_value += market_value
            
            # 统计行业分布
            industry = stock_info.get('industry', '未知')
            if industry:
                industry_distribution[industry] = industry_distribution.get(industry, 0) + 1

    return {
        'total_market_value': total_market_value,
        'stock_count': stock_count,
        'industry_distribution': industry_distribution
    }
