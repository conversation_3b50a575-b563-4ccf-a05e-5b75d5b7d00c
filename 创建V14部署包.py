#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建V14版本腾讯云托管部署包
"""

import os
import zipfile
import json
from datetime import datetime

def create_v14_deployment_package():
    """创建V14版本部署包"""
    
    # 部署包文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    zip_filename = f'持仓系统V14_腾讯云托管部署包_{timestamp}.zip'
    
    # V14版本核心文件
    v14_files = [
        'app.py',  # 已从持仓系统_v14.py复制
        'main.py',  # 入口文件
        'Dockerfile',  # V14专用
        'requirements.txt',
        'A股交易时间监测_简化版.py',
        'yearly_low_cache_reader.py',
        'tdx_scan_module.py',
    ]
    
    # 数据文件
    data_files = [
        'stock_data_cache.json',
        'imported_stock_list.json',
        'strategy_config.json',
        'auto_update_config.json',
        'reduction_monitoring_config.json',
    ]
    
    # 可选文件
    optional_files = [
        'wechat_alert_cache.pkl',
        '.gitignore'
    ]
    
    print(f"📦 创建V14版本腾讯云托管部署包: {zip_filename}")
    print("=" * 60)
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        
        # 打包V14核心文件
        print("📁 打包V14核心文件:")
        for file in v14_files:
            if os.path.exists(file):
                zipf.write(file)
                file_size = os.path.getsize(file)
                print(f"  ✅ {file} ({file_size:,} bytes)")
            else:
                print(f"  ❌ {file} - 文件不存在")
        
        # 打包数据文件
        print("\n📁 打包数据文件:")
        for file in data_files:
            if os.path.exists(file):
                zipf.write(file)
                file_size = os.path.getsize(file)
                print(f"  ✅ {file} ({file_size:,} bytes)")
            else:
                print(f"  ⚠️ {file} - 文件不存在（可选）")
        
        # 打包可选文件
        print("\n📁 打包可选文件:")
        for file in optional_files:
            if os.path.exists(file):
                zipf.write(file)
                file_size = os.path.getsize(file)
                print(f"  ✅ {file} ({file_size:,} bytes)")
            else:
                print(f"  ⚠️ {file} - 文件不存在（可选）")
        
        # 创建必要的目录结构
        print("\n📁 创建目录结构:")
        dirs_to_create = ['cache/', 'logs/', 'uploads/', 'data/']
        for dir_name in dirs_to_create:
            zipf.writestr(dir_name + '.gitkeep', '')
            print(f"  ✅ {dir_name}")
        
        # 创建部署说明
        deploy_info = {
            "package_name": zip_filename,
            "created_time": datetime.now().isoformat(),
            "version": "持仓系统V14",
            "platform": "腾讯云托管",
            "files_count": len(zipf.namelist()),
            "deployment_config": {
                "dockerfile": "Dockerfile",
                "port": 80,
                "startup_command": "python main.py",
                "memory": "512MB",
                "cpu": "0.25核"
            },
            "features": [
                "V14显示逻辑优化版",
                "减半后显示逻辑改进",
                "持仓数量直接编辑",
                "多策略卖出信号",
                "扫雷功能集成",
                "年内最低价缓存",
                "交易时间监测"
            ]
        }
        
        zipf.writestr('部署说明.json', json.dumps(deploy_info, ensure_ascii=False, indent=2))
        print(f"  ✅ 部署说明.json")
        
        # 创建README
        readme_content = '''# 持仓系统V14 - 腾讯云托管部署包

## 版本信息
- 版本: V14 显示逻辑优化版
- 平台: 腾讯云托管
- 部署方式: 压缩包上传

## 部署配置
- Dockerfile: Dockerfile
- 端口: 80
- 启动命令: python main.py
- 内存: 512MB
- CPU: 0.25核

## 部署步骤
1. 登录腾讯云控制台
2. 进入云托管服务
3. 选择"通过本地代码部署"
4. 上传此压缩包
5. 配置服务参数
6. 点击部署

## V14功能特性
- 显示逻辑优化
- 减半后显示改进
- 持仓数量直接编辑
- 多策略卖出信号
- 扫雷功能集成
- 年内最低价缓存
'''
        zipf.writestr('README.md', readme_content)
        print(f"  ✅ README.md")
    
    # 显示打包结果
    zip_size = os.path.getsize(zip_filename)
    print("\n" + "=" * 60)
    print(f"🎉 V14版本部署包创建完成!")
    print(f"📦 文件名: {zip_filename}")
    print(f"📊 大小: {zip_size:,} bytes ({zip_size/1024/1024:.2f} MB)")
    
    # 验证ZIP文件
    try:
        with zipfile.ZipFile(zip_filename, 'r') as zipf:
            file_count = len(zipf.namelist())
            print(f"📁 包含文件: {file_count} 个")
            print(f"✅ ZIP文件完整性验证通过")
    except Exception as e:
        print(f"❌ ZIP文件验证失败: {e}")
    
    print("\n🚀 上传这个压缩包:")
    print(f"📦 {zip_filename}")
    print("\n📋 腾讯云托管配置:")
    print("- Dockerfile名称: Dockerfile")
    print("- 端口: 80")
    print("- 启动命令: python main.py")
    
    return zip_filename

if __name__ == "__main__":
    create_v14_deployment_package()
