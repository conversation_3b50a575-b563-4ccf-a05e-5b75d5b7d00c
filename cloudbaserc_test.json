{"envId": "portfolio-system-9g8w6qhj8b8e8c8a", "functionRoot": "./", "functions": [{"name": "portfolio-test", "timeout": 60, "envVariables": {"TENCENTCLOUD_RUNENV": "SCF", "FLASK_ENV": "production"}, "runtime": "Python3.9", "memorySize": 512, "handler": "test_cloudbase.main_handler", "installDependency": true, "ignore": ["node_modules/**/*", ".git/**/*", "*.pyc", "__pycache__/**/*", ".DS_Store", "*.log", "uploads/**/*", "*.cache", "test_*", "*.md", "持仓系统_v13*", "app.py"]}]}