#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CloudBase兼容性验证脚本
====================

验证持仓系统V14是否已正确改造为CloudBase兼容版本

Author: AI Assistant
Date: 2025-07-30
"""

import os
import sys
import importlib.util

def check_file_exists(filename, description):
    """检查文件是否存在"""
    exists = os.path.exists(filename)
    status = "✅" if exists else "❌"
    print(f"{status} {description}: {filename}")
    return exists

def check_import(module_name, description):
    """检查模块是否可以导入"""
    try:
        if module_name.endswith('.py'):
            # 对于.py文件，使用spec导入
            spec = importlib.util.spec_from_file_location("test_module", module_name)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
        else:
            # 对于模块名，直接导入
            __import__(module_name)
        print(f"✅ {description}: 导入成功")
        return True
    except Exception as e:
        print(f"❌ {description}: 导入失败 - {str(e)}")
        return False

def check_function_exists(module_name, function_name, description):
    """检查函数是否存在"""
    try:
        if module_name.endswith('.py'):
            spec = importlib.util.spec_from_file_location("test_module", module_name)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
        else:
            module = __import__(module_name)
        
        if hasattr(module, function_name):
            print(f"✅ {description}: {function_name} 函数存在")
            return True
        else:
            print(f"❌ {description}: {function_name} 函数不存在")
            return False
    except Exception as e:
        print(f"❌ {description}: 检查失败 - {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证CloudBase兼容性...")
    print("=" * 50)
    
    # 检查必要文件
    print("\n📁 检查必要文件:")
    files_check = [
        ("持仓系统_v14.py", "主应用文件"),
        ("portfolio_system_v14.py", "英文文件名wrapper"),
        ("requirements.txt", "依赖文件"),
        ("cloudbaserc_v14.json", "CloudBase配置文件"),
        ("test_cloudbase.py", "测试应用"),
        ("deploy_cloudbase_v14.bat", "Windows部署脚本"),
        ("deploy_cloudbase_v14.sh", "Linux部署脚本"),
    ]
    
    file_results = []
    for filename, desc in files_check:
        result = check_file_exists(filename, desc)
        file_results.append(result)
    
    # 检查主应用导入
    print("\n📦 检查模块导入:")
    import_results = []
    
    # 检查主应用
    result = check_import("持仓系统_v14.py", "主应用模块")
    import_results.append(result)
    
    # 检查wrapper
    result = check_import("portfolio_system_v14.py", "Wrapper模块")
    import_results.append(result)
    
    # 检查测试应用
    result = check_import("test_cloudbase.py", "测试应用模块")
    import_results.append(result)
    
    # 检查关键函数
    print("\n🔧 检查关键函数:")
    function_results = []
    
    # 检查main_handler函数
    result = check_function_exists("持仓系统_v14.py", "main_handler", "主应用CloudBase入口")
    function_results.append(result)
    
    result = check_function_exists("portfolio_system_v14.py", "main_handler", "Wrapper CloudBase入口")
    function_results.append(result)
    
    result = check_function_exists("test_cloudbase.py", "main_handler", "测试应用CloudBase入口")
    function_results.append(result)
    
    # 检查init_app函数
    result = check_function_exists("持仓系统_v14.py", "init_app", "应用初始化函数")
    function_results.append(result)
    
    # 检查环境变量兼容性
    print("\n🌍 检查环境变量兼容性:")
    env_results = []
    
    try:
        # 模拟CloudBase环境
        os.environ['TENCENTCLOUD_RUNENV'] = 'SCF'
        
        # 尝试导入并检查IS_CLOUDBASE变量
        spec = importlib.util.spec_from_file_location("main_app", "持仓系统_v14.py")
        main_app = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_app)
        
        if hasattr(main_app, 'IS_CLOUDBASE') and main_app.IS_CLOUDBASE:
            print("✅ CloudBase环境检测: 正确识别CloudBase环境")
            env_results.append(True)
        else:
            print("❌ CloudBase环境检测: 未正确识别CloudBase环境")
            env_results.append(False)
            
        # 清理环境变量
        if 'TENCENTCLOUD_RUNENV' in os.environ:
            del os.environ['TENCENTCLOUD_RUNENV']
            
    except Exception as e:
        print(f"❌ 环境变量检测失败: {str(e)}")
        env_results.append(False)
    
    # 生成验证报告
    print("\n" + "=" * 50)
    print("📊 验证结果汇总:")
    
    total_checks = len(file_results) + len(import_results) + len(function_results) + len(env_results)
    passed_checks = sum(file_results) + sum(import_results) + sum(function_results) + sum(env_results)
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"通过率: {passed_checks/total_checks*100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 恭喜！所有检查都通过了，CloudBase兼容性改造完成！")
        print("\n📋 下一步操作:")
        print("1. 运行测试应用: python test_cloudbase.py")
        print("2. 部署到CloudBase: ./deploy_cloudbase_v14.sh")
        print("3. 验证线上功能: 访问CloudBase提供的URL")
        return True
    else:
        print(f"\n⚠️ 发现 {total_checks - passed_checks} 个问题，请检查并修复后重新验证")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
