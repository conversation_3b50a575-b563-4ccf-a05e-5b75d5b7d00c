# 腾讯云CloudBase稳定版Dockerfile - 解决网络问题
FROM python:3.11-slim

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置Python环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV FLASK_ENV=production

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制所有文件
COPY . /app/

# 创建pip配置文件，强制使用国内镜像
RUN mkdir -p ~/.pip && \
    echo "[global]" > ~/.pip/pip.conf && \
    echo "index-url = https://mirrors.aliyun.com/pypi/simple/" >> ~/.pip/pip.conf && \
    echo "trusted-host = mirrors.aliyun.com" >> ~/.pip/pip.conf

# 安装Python依赖（使用超时和重试机制）
RUN pip install --no-cache-dir --timeout 60 --retries 3 -r requirements.txt

# 创建必要的目录
RUN mkdir -p uploads cache logs data

# 设置权限
RUN chmod +x main.py

# 暴露端口
EXPOSE 8080

# 启动应用
CMD ["python", "main.py"]
