# A股交易时间监测模块 - 简化版
# 功能：判断当前是否为A股交易时间，包含集合竞价

import datetime
import json
import os
import requests
import time
from typing import Tuple, List, Dict, Optional

class AStockTradingTimeMonitor:
    """A股交易时间监测器 - 简化版"""
    
    def __init__(self):
        # A股交易时间配置（包含集合竞价）
        self.trading_sessions = [
            # 集合竞价时间
            (datetime.time(9, 15), datetime.time(9, 25), "集合竞价"),
            # 连续竞价时间  
            (datetime.time(9, 30), datetime.time(11, 30), "上午交易"),
            (datetime.time(13, 0), datetime.time(15, 0), "下午交易"),
            # 尾盘集合竞价（包含在下午交易中，但可以单独识别）
            (datetime.time(14, 57), datetime.time(15, 0), "尾盘集合竞价")
        ]
        
        # 节假日缓存文件
        self.holiday_cache_file = "节假日缓存.json"
        
        # 加载节假日缓存
        self.holidays_cache = self._load_holiday_cache()
    
    def _load_holiday_cache(self) -> Dict[str, List[str]]:
        """加载节假日缓存"""
        cache_data = {}

        # 尝试从缓存文件加载
        if os.path.exists(self.holiday_cache_file):
            try:
                with open(self.holiday_cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    print(f"✅ 从缓存加载节假日数据: {list(cache_data.keys())}")
            except Exception as e:
                print(f"⚠️ 加载节假日缓存失败: {e}")

        # 检查是否需要更新当年数据
        current_year = str(datetime.date.today().year)
        if current_year not in cache_data:
            print(f"📡 正在从API获取{current_year}年节假日数据...")
            api_data = self._fetch_holidays_from_api(current_year)
            if api_data:
                cache_data[current_year] = api_data
                self._save_holiday_cache(cache_data)
            else:
                # API失败时使用默认数据
                print(f"⚠️ API获取失败，使用默认节假日数据")
                cache_data[current_year] = self._get_default_holidays(current_year)

        return cache_data

    def _fetch_holidays_from_api(self, year: str) -> List[str]:
        """从API获取指定年份的节假日"""
        try:
            url = f"http://timor.tech/api/holiday/year/{year}/"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                data = response.json()

                if data.get('code') == 0 and 'holiday' in data:
                    holidays = []
                    for date_key, holiday_info in data['holiday'].items():
                        if holiday_info and holiday_info.get('holiday', False):
                            # 转换为完整日期格式
                            full_date = f"{year}-{date_key}"
                            holidays.append(full_date)

                    print(f"✅ 成功从API获取{year}年节假日数据，共{len(holidays)}天")
                    return holidays
                else:
                    print(f"❌ API返回数据格式错误: {data}")
            else:
                print(f"❌ API请求失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"❌ 获取节假日API失败: {e}")

        return []

    def _get_default_holidays(self, year: str) -> List[str]:
        """获取默认节假日数据（API失败时使用）"""
        if year == "2025":
            return [
                # 元旦
                "2025-01-01",
                # 春节（大概时间）
                "2025-01-28", "2025-01-29", "2025-01-30", "2025-01-31",
                "2025-02-01", "2025-02-02", "2025-02-03", "2025-02-04",
                # 清明节
                "2025-04-05", "2025-04-06", "2025-04-07",
                # 劳动节
                "2025-05-01", "2025-05-02", "2025-05-03",
                # 国庆节
                "2025-10-01", "2025-10-02", "2025-10-03", "2025-10-04",
                "2025-10-05", "2025-10-06", "2025-10-07"
            ]
        return []

    def _save_holiday_cache(self, cache_data: Dict[str, List[str]]) -> None:
        """保存节假日缓存"""
        try:
            with open(self.holiday_cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 节假日缓存已保存")
        except Exception as e:
            print(f"❌ 保存节假日缓存失败: {e}")

    def update_holiday_cache(self, year: str, holidays: List[str] = None) -> None:
        """更新节假日缓存"""
        if holidays is None:
            # 如果没有提供节假日列表，从API获取
            print(f"📡 正在从API更新{year}年节假日数据...")
            holidays = self._fetch_holidays_from_api(year)
            if not holidays:
                print(f"❌ 无法从API获取{year}年节假日数据")
                return

        self.holidays_cache[year] = holidays
        self._save_holiday_cache(self.holidays_cache)
        print(f"✅ 已更新{year}年节假日缓存，共{len(holidays)}天")

    def refresh_current_year_holidays(self) -> bool:
        """刷新当年节假日数据"""
        current_year = str(datetime.date.today().year)
        try:
            self.update_holiday_cache(current_year)
            return True
        except Exception as e:
            print(f"❌ 刷新{current_year}年节假日失败: {e}")
            return False
    
    def is_trading_day(self, check_date: Optional[datetime.date] = None) -> bool:
        """
        判断是否为交易日
        :param check_date: 要检查的日期，默认为今天
        :return: True表示交易日，False表示非交易日
        """
        if check_date is None:
            check_date = datetime.date.today()
        
        # 检查是否为周末
        if check_date.weekday() >= 5:  # 周六=5, 周日=6
            return False
        
        # 检查是否为节假日
        year = str(check_date.year)
        if year in self.holidays_cache:
            date_str = check_date.strftime("%Y-%m-%d")
            if date_str in self.holidays_cache[year]:
                return False
        
        return True
    
    def is_trading_time(self, check_time: Optional[datetime.datetime] = None, include_auction: bool = True) -> bool:
        """
        判断是否为交易时间
        :param check_time: 要检查的时间，默认为当前时间
        :param include_auction: 是否包含集合竞价时间
        :return: True表示交易时间，False表示非交易时间
        """
        if check_time is None:
            check_time = datetime.datetime.now()
        
        # 首先检查是否为交易日
        if not self.is_trading_day(check_time.date()):
            return False
        
        # 检查是否在交易时间段内
        current_time = check_time.time()
        
        for start_time, end_time, session_type in self.trading_sessions:
            if start_time <= current_time <= end_time:
                # 如果不包含集合竞价，跳过集合竞价时间
                if not include_auction and "集合竞价" in session_type:
                    continue
                return True
        
        return False
    
    def get_current_session(self, check_time: Optional[datetime.datetime] = None) -> str:
        """
        获取当前交易时段
        :param check_time: 要检查的时间，默认为当前时间
        :return: 交易时段描述
        """
        if check_time is None:
            check_time = datetime.datetime.now()
        
        if not self.is_trading_day(check_time.date()):
            return "休市"
        
        current_time = check_time.time()
        
        for start_time, end_time, session_type in self.trading_sessions:
            if start_time <= current_time <= end_time:
                return session_type
        
        return "休市"
    
    def should_update_data(self, include_auction: bool = False) -> bool:
        """
        判断是否应该更新股票数据
        :param include_auction: 是否在集合竞价时间也更新数据
        :return: True表示应该更新，False表示不应该更新
        """
        return self.is_trading_time(include_auction=include_auction)
    
    def get_update_interval(self, rest_interval=600) -> int:
        """
        根据交易状态获取建议的更新间隔（秒）
        :param rest_interval: 休市时间的检查间隔（秒），默认600秒
        :return: 更新间隔秒数
        """
        current_session = self.get_current_session()

        if "集合竞价" in current_session:
            return 10  # 集合竞价时间10秒更新一次
        elif self.is_trading_time(include_auction=False):
            return 300  # 连续竞价时间30秒更新一次
        else:
            return rest_interval  # 休市时间使用配置的间隔

    def get_trading_status(self) -> dict:
        """
        获取当前交易状态信息
        :return: 包含交易状态的字典
        """
        now = datetime.datetime.now()
        is_trading = self.is_trading_time(now)
        current_session = self.get_current_session(now)
        
        if is_trading:
            return {
                'is_trading': True,
                'status': '交易中',
                'session': current_session,
                'message': f'正在{current_session}'
            }
        else:
            return {
                'is_trading': False,
                'status': '休市中',
                'session': current_session,
                'message': f'当前{current_session}'
            }

# 测试函数
def test_trading_time_monitor():
    """测试交易时间监测功能"""
    monitor = AStockTradingTimeMonitor()
    
    print("=== A股交易时间监测测试 ===")
    print(f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 获取交易状态
    status = monitor.get_trading_status()
    print("交易状态信息:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    print()
    
    # 测试几个特定时间
    test_times = [
        datetime.datetime(2025, 7, 21, 9, 0),   # 开盘前
        datetime.datetime(2025, 7, 21, 9, 20),  # 集合竞价时间
        datetime.datetime(2025, 7, 21, 10, 0),  # 上午交易时间
        datetime.datetime(2025, 7, 21, 12, 0),  # 午休时间
        datetime.datetime(2025, 7, 21, 14, 0),  # 下午交易时间
        datetime.datetime(2025, 7, 21, 14, 58), # 尾盘集合竞价
        datetime.datetime(2025, 7, 21, 16, 0),  # 收盘后
        datetime.datetime(2025, 7, 19, 10, 0),  # 周六
    ]
    
    print("特定时间测试:")
    for test_time in test_times:
        is_trading = monitor.is_trading_time(test_time)
        session = monitor.get_current_session(test_time)
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][test_time.weekday()]
        print(f"  {test_time.strftime('%Y-%m-%d %H:%M')} ({weekday}): {session} {'✅' if is_trading else '❌'}")
    
    print()
    print(f"是否应该更新数据: {'是' if monitor.should_update_data() else '否'}")
    print(f"是否应该更新数据(含集合竞价): {'是' if monitor.should_update_data(include_auction=True) else '否'}")
    print(f"建议更新间隔: {monitor.get_update_interval()}秒")

if __name__ == "__main__":
    test_trading_time_monitor()
