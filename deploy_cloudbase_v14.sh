#!/bin/bash

echo "🚀 开始部署持仓系统V14到腾讯云CloudBase..."
echo ""

# 检查是否安装了CloudBase CLI
if ! command -v tcb &> /dev/null; then
    echo "❌ 未找到腾讯云CLI工具，请先安装："
    echo "   npm install -g @cloudbase/cli"
    exit 1
fi

# 检查是否已登录
echo "📝 检查登录状态..."
if ! tcb auth:list &> /dev/null; then
    echo "🔑 请先登录腾讯云："
    tcb login
fi

# 检查必要文件
echo "📋 检查部署文件..."
if [ ! -f "持仓系统_v14.py" ]; then
    echo "❌ 未找到主程序文件：持仓系统_v14.py"
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "❌ 未找到依赖文件：requirements.txt"
    exit 1
fi

if [ ! -f "cloudbaserc_v14.json" ]; then
    echo "❌ 未找到配置文件：cloudbaserc_v14.json"
    exit 1
fi

# 使用V14配置文件
echo "🔧 使用V14配置文件..."
cp cloudbaserc_v14.json cloudbaserc.json

# 创建临时目录并复制必要文件
echo "📦 准备部署文件..."
mkdir -p temp_deploy
cp 持仓系统_v14.py temp_deploy/
cp requirements.txt temp_deploy/
cp cloudbaserc.json temp_deploy/

# 复制依赖模块（如果存在）
if [ -f "A股交易时间监测_简化版.py" ]; then
    cp A股交易时间监测_简化版.py temp_deploy/
    echo "✅ 已复制交易时间监测模块"
fi

if [ -f "yearly_low_cache_reader.py" ]; then
    cp yearly_low_cache_reader.py temp_deploy/
    echo "✅ 已复制年内最低价缓存模块"
fi

if [ -f "tdx_scan_module.py" ]; then
    cp tdx_scan_module.py temp_deploy/
    echo "✅ 已复制扫雷模块"
fi

# 进入临时目录
cd temp_deploy

# 部署函数
echo "📦 开始部署云函数..."
tcb functions:deploy portfolio-system-v14

# 检查部署结果
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 部署完成！"
    echo "🌐 访问地址: https://portfolio-system-9g8w6qhj8b8e8c8a.ap-shanghai.app.tcloudbase.com/"
    echo ""
    echo "📊 查看函数状态:"
    tcb functions:list
    echo ""
    echo "📝 查看函数日志:"
    echo "   tcb functions:log portfolio-system-v14"
    echo ""
    echo "🔧 更新函数配置:"
    echo "   tcb functions:config:update portfolio-system-v14"
else
    echo "❌ 部署失败，请检查错误信息"
    cd ..
    rm -rf temp_deploy
    exit 1
fi

# 清理临时文件
cd ..
rm -rf temp_deploy
rm -f cloudbaserc.json

echo "🧹 清理完成"
echo "🎉 部署流程结束"
