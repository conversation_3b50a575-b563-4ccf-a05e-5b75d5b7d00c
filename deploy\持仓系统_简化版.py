#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓系统简化版 - 专为腾讯云部署
================================
"""

from flask import Flask, render_template_string, jsonify, request
import requests
import pandas as pd
import os
from datetime import datetime

app = Flask(__name__)

# 配置
app.config['UPLOAD_FOLDER'] = 'uploads'
UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')

# 确保上传目录存在
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# 全局变量
stock_data = {}
last_update_time = None

def get_stock_info(stock_code):
    """获取股票信息"""
    try:
        # 这里可以添加股票数据获取逻辑
        return {
            'code': stock_code,
            'name': f'股票{stock_code}',
            'price': 10.00,
            'change_pct': 1.5,
            'update_time': datetime.now().strftime('%H:%M:%S')
        }
    except Exception as e:
        print(f"获取股票信息失败: {e}")
        return None

@app.route('/')
def index():
    """主页"""
    html_template = '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>持仓系统</title>
        <style>
            body {
                font-family: 'Microsoft YaHei', Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                color: #333;
                margin-bottom: 30px;
            }
            .status {
                background: #e8f5e8;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                text-align: center;
            }
            .success {
                color: #28a745;
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                color: #666;
                margin-top: 10px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏦 持仓系统</h1>
                <p>腾讯云CloudBase部署成功！</p>
            </div>
            
            <div class="status">
                <div class="success">✅ 系统运行正常</div>
                <div class="info">
                    <p>部署时间: {{ current_time }}</p>
                    <p>运行环境: 腾讯云CloudBase</p>
                    <p>Python版本: {{ python_version }}</p>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <h3>🎉 恭喜！你的持仓系统已成功部署到云端！</h3>
                <p>现在任何人都可以通过这个网址访问你的系统了。</p>
                <p style="color: #666; font-size: 14px;">
                    如需添加更多功能，请联系开发者进行定制。
                </p>
            </div>
        </div>
    </body>
    </html>
    '''
    
    import sys
    return render_template_string(html_template, 
                                current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                python_version=sys.version.split()[0])

@app.route('/api/test')
def api_test():
    """API测试接口"""
    return jsonify({
        'status': 'success',
        'message': '持仓系统API运行正常',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0'
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    # 启动时加载数据等初始化操作可以在这里添加
    print("🚀 启动持仓系统...")
    
    # 获取端口
    port = int(os.environ.get('PORT', 8080))
    
    # 生产环境配置
    if os.environ.get('FLASK_ENV') == 'production':
        print(f"🌐 生产环境启动，端口: {port}")
        app.run(host='0.0.0.0', port=port, debug=False, threaded=True)
    else:
        print(f"🔧 开发环境启动，端口: {port}")
        app.run(host='0.0.0.0', port=port, debug=True)
