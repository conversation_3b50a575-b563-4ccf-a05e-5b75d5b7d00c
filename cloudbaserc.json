{"envId": "portfolio-system-9g8w6qhj8b8e8c8a", "functionRoot": "./", "functions": [{"name": "portfolio-system-v14", "timeout": 120, "envVariables": {"WECHAT_WEBHOOK_URL": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7bddd706-ef0f-4d6c-9e1f-4d6c-9e1f-3cc8a0a18d9e", "TENCENTCLOUD_RUNENV": "SCF", "FLASK_ENV": "production"}, "runtime": "Python3.9", "memorySize": 1024, "handler": "持仓系统_v14.main_handler", "installDependency": true, "ignore": ["node_modules/**/*", ".git/**/*", "*.pyc", "__pycache__/**/*", ".DS_Store", "*.log", "uploads/**/*", "*.cache", "test_*", "*.md"]}], "hosting": {"root": "./static", "ignore": [".git", "node_modules"]}}