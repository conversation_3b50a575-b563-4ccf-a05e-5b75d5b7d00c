#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略管理模块
============

负责管理股票卖出策略，包括：
- 策略配置管理
- 默认策略工厂
- 策略执行逻辑
- 卖出信号计算

作者: AI Assistant
版本: 1.0
"""

import json
import os
from typing import Dict, List, Optional, Tuple


class StrategyConfig:
    """策略配置类 - 管理策略的基本信息和参数"""

    def __init__(self, name, description, enabled=True, params=None, weight=1.0, category='default', action='hold'):
        self.name = name
        self.description = description
        self.enabled = enabled
        self.params = params or {}
        self.weight = weight
        self.category = category
        self.action = action

    def to_dict(self):
        """转换为字典格式"""
        return {
            'name': self.name,
            'description': self.description,
            'enabled': self.enabled,
            'params': self.params,
            'weight': self.weight,
            'category': self.category,
            'action': self.action
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建策略配置"""
        return cls(
            name=data.get('name', ''),
            description=data.get('description', ''),
            enabled=data.get('enabled', True),
            params=data.get('params', {}),
            weight=data.get('weight', 1.0),
            category=data.get('category', 'default'),
            action=data.get('action', 'hold')
        )


class DefaultStrategyFactory:
    """默认策略工厂 - 创建系统预设策略"""

    @staticmethod
    def create_default_strategies():
        """创建所有默认策略"""
        strategies = {}

        # 1. 基础涨幅策略
        strategies.update(DefaultStrategyFactory._create_basic_gain_strategies())

        # 2. 涨停+PB策略
        strategies.update(DefaultStrategyFactory._create_limit_up_pb_strategies())

        # 3. 高TTM策略
        strategies.update(DefaultStrategyFactory._create_high_ttm_strategies())

        # 4. 高TTM+涨停策略
        strategies.update(DefaultStrategyFactory._create_high_ttm_limit_up_strategies())

        # 5. 负TTM+低股息+低扫雷策略
        strategies.update(DefaultStrategyFactory._create_negative_ttm_risk_strategies())

        # 6. 负TTM回本策略
        strategies.update(DefaultStrategyFactory._create_negative_ttm_profit_strategies())

        return strategies

    @staticmethod
    def _create_basic_gain_strategies():
        """创建基础涨幅策略"""
        return {
            'basic_gain_reduce': StrategyConfig(
                name='基础涨幅减半策略',
                description='涨幅≥70%时减半',
                params={'gain_threshold': 70.0, 'warning_offset': 5.0},
                weight=1.0,
                category='basic_gain',
                action='reduce'
            ).to_dict(),
            'basic_gain_clearance': StrategyConfig(
                name='基础涨幅清仓策略',
                description='涨幅≥140%时清仓',
                params={'gain_threshold': 140.0, 'warning_offset': 10.0},
                weight=2.0,
                category='basic_gain',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_limit_up_pb_strategies():
        """创建涨停+PB策略"""
        return {
            'limit_up_high_pb_clearance': StrategyConfig(
                name='涨停高PB清仓策略',
                description='涨停 且 PB≥1.75 且 涨幅≥70%时清仓',
                params={
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'gain_threshold': 70.0,
                    'warning_offset': 5.0
                },
                weight=2.0,
                category='limit_up_pb',
                action='clearance'
            ).to_dict(),
            'limit_up_low_pb_reduce': StrategyConfig(
                name='涨停低PB减半策略',
                description='涨停 且 PB≤1.75 且 涨幅≥70%时减半',
                params={
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'gain_threshold': 70.0,
                    'warning_offset': 5.0
                },
                weight=1.5,
                category='limit_up_pb',
                action='reduce'
            ).to_dict()
        }

    @staticmethod
    def _create_high_ttm_strategies():
        """创建高TTM策略"""
        return {
            'high_ttm_reduce': StrategyConfig(
                name='高TTM减半策略',
                description='TTM≥30 且 涨幅≥50%时减半',
                params={
                    'ttm_threshold': 30.0,
                    'gain_threshold': 50.0,
                    'warning_offset': 5.0
                },
                weight=1.2,
                category='high_ttm',
                action='reduce'
            ).to_dict(),
            'high_ttm_clearance': StrategyConfig(
                name='高TTM清仓策略',
                description='TTM≥30 且 涨幅≥100%时清仓',
                params={
                    'ttm_threshold': 30.0,
                    'gain_threshold': 100.0,
                    'warning_offset': 10.0
                },
                weight=2.0,
                category='high_ttm',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_high_ttm_limit_up_strategies():
        """创建高TTM+涨停策略"""
        return {
            'high_ttm_limit_up_high_pb_clearance': StrategyConfig(
                name='高TTM涨停高PB清仓策略',
                description='TTM≥30 且 涨停 且 PB≥1.75 且 涨幅≥50%时清仓',
                params={
                    'ttm_threshold': 30.0,
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'gain_threshold': 50.0,
                    'warning_offset': 5.0
                },
                weight=2.5,
                category='high_ttm_limit_up',
                action='clearance'
            ).to_dict(),
            'high_ttm_limit_up_low_pb_reduce': StrategyConfig(
                name='高TTM涨停低PB减半策略',
                description='TTM≥30 且 涨停 且 PB≤1.75 且 涨幅≥50%时减半',
                params={
                    'ttm_threshold': 30.0,
                    'limit_up_threshold': 9.8,
                    'pb_threshold': 1.75,
                    'gain_threshold': 50.0,
                    'warning_offset': 5.0
                },
                weight=1.8,
                category='high_ttm_limit_up',
                action='reduce'
            ).to_dict()
        }

    @staticmethod
    def _create_negative_ttm_risk_strategies():
        """创建负TTM+低股息+低扫雷策略"""
        return {
            'negative_ttm_low_dividend_low_scan_reduce': StrategyConfig(
                name='负TTM低股息低扫雷减半策略',
                description='TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥30%时减半',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 2.0,
                    'scan_score_max': 70,
                    'gain_threshold': 30.0,
                    'warning_offset': 5.0
                },
                weight=1.3,
                category='negative_ttm_risk',
                action='reduce'
            ).to_dict(),
            'negative_ttm_low_dividend_low_scan_clearance': StrategyConfig(
                name='负TTM低股息低扫雷清仓策略',
                description='TTM≤0 且 股息≤2% 且 扫雷分数≤70 且 涨幅≥50%时清仓',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 2.0,
                    'scan_score_max': 70,
                    'gain_threshold': 50.0,
                    'warning_offset': 5.0
                },
                weight=2.2,
                category='negative_ttm_risk',
                action='clearance'
            ).to_dict()
        }

    @staticmethod
    def _create_negative_ttm_profit_strategies():
        """创建负TTM回本策略"""
        return {
            'negative_ttm_profit_clearance': StrategyConfig(
                name='负TTM回本清仓策略',
                description='TTM≤0 且 股息≤3% 且 接近/已回本时清仓',
                params={
                    'ttm_max': 0.0,
                    'dividend_max': 3.0,
                    'require_profit': True,
                    'profit_threshold': 10.0
                },
                weight=2.0,
                category='negative_ttm_profit',
                action='clearance'
            ).to_dict()
        }


class SellStrategyManager:
    """卖出策略管理器 - 优化版本"""

    def __init__(self, config_file='config/strategy_config.json'):
        self.strategies = {}
        self.config_file = config_file
        print("🔧 初始化策略管理器...")
        self._initialize_strategies()

    def _initialize_strategies(self):
        """初始化策略配置"""
        self.strategies = DefaultStrategyFactory.create_default_strategies()
        self.load_strategy_config()  # 加载保存的配置（会覆盖默认配置）

    def load_strategy_config(self):
        """从文件加载策略配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    print(f"📂 从文件加载策略配置，包含 {len(saved_config)} 个策略")

                    # 完全替换策略配置（包括自定义策略）
                    self.strategies = saved_config

                    # 打印加载的策略
                    self._print_strategy_status(saved_config)

            else:
                print("⚠️ 策略配置文件不存在，使用默认配置")
                self._print_strategy_status(self.strategies)
        except Exception as e:
            print(f"❌ 加载策略配置失败: {e}")
            print("🔄 将使用默认策略配置")

    def save_strategy_config(self):
        """保存策略配置到文件"""
        try:
            # 确保配置目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategies, f, ensure_ascii=False, indent=2)
            print(f"💾 策略配置已保存到文件")

            # 打印当前策略状态用于调试
            self._print_strategy_status(self.strategies)
        except Exception as e:
            print(f"❌ 保存策略配置失败: {e}")

    def _print_strategy_status(self, strategies):
        """打印策略状态"""
        for strategy_id, config in strategies.items():
            category = config.get('category', 'unknown')
            enabled = config.get('enabled', False)
            name = config.get('name', strategy_id)
            print(f"  📋 {name} ({category}): {'✅' if enabled else '❌'}")

    def get_enabled_strategies(self):
        """获取所有启用的策略"""
        return {
            strategy_id: strategy
            for strategy_id, strategy in self.strategies.items()
            if strategy.get('enabled', False)
        }

    def update_strategy(self, strategy_id: str, config: dict):
        """更新单个策略配置"""
        if strategy_id in self.strategies:
            self.strategies[strategy_id].update(config)
            self.save_strategy_config()
            return True
        return False

    def add_custom_strategy(self, strategy_id: str, config: dict):
        """添加自定义策略"""
        self.strategies[strategy_id] = config
        self.save_strategy_config()

    def remove_strategy(self, strategy_id: str):
        """删除策略"""
        if strategy_id in self.strategies:
            del self.strategies[strategy_id]
            self.save_strategy_config()
            return True
        return False

    def evaluate_strategy(self, strategy_id: str, stock_data: dict) -> Optional[dict]:
        """评估单个策略是否触发"""
        if strategy_id not in self.strategies:
            return None

        strategy = self.strategies[strategy_id]
        if not strategy.get('enabled', False):
            return None

        params = strategy.get('params', {})
        action = strategy.get('action', 'hold')

        # 获取股票数据
        current_price = stock_data.get('price', 0)
        change_pct = stock_data.get('change_pct', 0)
        pe_ratio = stock_data.get('pe_ratio', 0)
        pb_ratio = stock_data.get('pb_ratio', 0)
        dividend_yield = stock_data.get('dividend_yield', 0)
        distance_from_low_pct = stock_data.get('distance_from_low_pct', 0)

        # 获取扫雷分数
        scan_score = stock_data.get('scan_score', 0)

        # 基础涨幅策略
        if strategy_id.startswith('basic_gain'):
            gain_threshold = params.get('gain_threshold', 70)
            warning_offset = params.get('warning_offset', 5)

            if distance_from_low_pct >= gain_threshold:
                return {
                    'signal': action,
                    'reason': f'涨幅{distance_from_low_pct:.1f}%≥{gain_threshold}%',
                    'score': distance_from_low_pct,
                    'strategy': strategy['name']
                }
            elif distance_from_low_pct >= gain_threshold - warning_offset:
                return {
                    'signal': 'warning',
                    'reason': f'涨幅{distance_from_low_pct:.1f}%接近{gain_threshold}%',
                    'score': distance_from_low_pct,
                    'strategy': strategy['name']
                }

        # 涨停+PB策略
        elif strategy_id.startswith('limit_up'):
            limit_up_threshold = params.get('limit_up_threshold', 9.8)
            pb_threshold = params.get('pb_threshold', 1.75)
            gain_threshold = params.get('gain_threshold', 70)
            warning_offset = params.get('warning_offset', 5)

            is_limit_up = change_pct >= limit_up_threshold
            pb_condition = (pb_ratio >= pb_threshold) if 'high_pb' in strategy_id else (pb_ratio <= pb_threshold)
            gain_condition = distance_from_low_pct >= gain_threshold

            if is_limit_up and pb_condition and gain_condition:
                pb_desc = f'PB{pb_ratio:.2f}≥{pb_threshold}' if 'high_pb' in strategy_id else f'PB{pb_ratio:.2f}≤{pb_threshold}'
                return {
                    'signal': action,
                    'reason': f'涨停+{pb_desc}+涨幅{distance_from_low_pct:.1f}%',
                    'score': distance_from_low_pct + change_pct,
                    'strategy': strategy['name']
                }

        # 高TTM策略
        elif strategy_id.startswith('high_ttm'):
            ttm_threshold = params.get('ttm_threshold', 30)
            gain_threshold = params.get('gain_threshold', 50)
            warning_offset = params.get('warning_offset', 5)

            # 处理涨停+PB组合策略
            if 'limit_up' in strategy_id:
                limit_up_threshold = params.get('limit_up_threshold', 9.8)
                pb_threshold = params.get('pb_threshold', 1.75)

                is_limit_up = change_pct >= limit_up_threshold
                pb_condition = (pb_ratio >= pb_threshold) if 'high_pb' in strategy_id else (pb_ratio <= pb_threshold)
                ttm_condition = pe_ratio >= ttm_threshold
                gain_condition = distance_from_low_pct >= gain_threshold

                if is_limit_up and pb_condition and ttm_condition and gain_condition:
                    pb_desc = f'PB{pb_ratio:.2f}≥{pb_threshold}' if 'high_pb' in strategy_id else f'PB{pb_ratio:.2f}≤{pb_threshold}'
                    return {
                        'signal': action,
                        'reason': f'高TTM{pe_ratio:.1f}+涨停+{pb_desc}',
                        'score': distance_from_low_pct + change_pct + pe_ratio,
                        'strategy': strategy['name']
                    }
            else:
                # 普通高TTM策略
                ttm_condition = pe_ratio >= ttm_threshold
                gain_condition = distance_from_low_pct >= gain_threshold

                if ttm_condition and gain_condition:
                    return {
                        'signal': action,
                        'reason': f'高TTM{pe_ratio:.1f}+涨幅{distance_from_low_pct:.1f}%',
                        'score': distance_from_low_pct + pe_ratio,
                        'strategy': strategy['name']
                    }

        # 负TTM策略
        elif strategy_id.startswith('negative_ttm'):
            ttm_max = params.get('ttm_max', 0)
            dividend_max = params.get('dividend_max', 2)

            if 'profit' in strategy_id:
                # 负TTM回本策略
                require_profit = params.get('require_profit', True)
                profit_threshold = params.get('profit_threshold', 10)

                ttm_condition = pe_ratio <= ttm_max
                dividend_condition = dividend_yield <= dividend_max
                profit_condition = distance_from_low_pct >= -profit_threshold if require_profit else True

                if ttm_condition and dividend_condition and profit_condition:
                    return {
                        'signal': action,
                        'reason': f'负TTM{pe_ratio:.1f}+低股息{dividend_yield:.1f}%+接近回本',
                        'score': abs(pe_ratio) + (100 - dividend_yield),
                        'strategy': strategy['name']
                    }
            else:
                # 负TTM风险策略
                scan_score_max = params.get('scan_score_max', 70)
                gain_threshold = params.get('gain_threshold', 30)
                warning_offset = params.get('warning_offset', 5)

                ttm_condition = pe_ratio <= ttm_max
                dividend_condition = dividend_yield <= dividend_max
                scan_condition = scan_score <= scan_score_max
                gain_condition = distance_from_low_pct >= gain_threshold

                if ttm_condition and dividend_condition and scan_condition and gain_condition:
                    return {
                        'signal': action,
                        'reason': f'负TTM{pe_ratio:.1f}+低股息{dividend_yield:.1f}%+低扫雷{scan_score}',
                        'score': abs(pe_ratio) + (100 - dividend_yield) + (100 - scan_score),
                        'strategy': strategy['name']
                    }

        return None
