# 持仓系统云部署指南

## 快速部署步骤

### 1. 准备GitHub仓库

1. 在GitHub上创建一个新仓库
2. 将本地代码上传到GitHub：

```bash
git init
git add .
git commit -m "初始提交"
git branch -M main
git remote add origin https://github.com/你的用户名/你的仓库名.git
git push -u origin main
```

### 2. 使用Railway部署（推荐）

1. 访问 https://railway.app
2. 使用GitHub账号登录
3. 点击 "New Project"
4. 选择 "Deploy from GitHub repo"
5. 选择你刚创建的仓库
6. Railway会自动检测到这是一个Python项目并开始部署
7. 部署完成后，你会得到一个公网访问地址

### 3. 使用Render部署

1. 访问 https://render.com
2. 使用GitHub账号登录
3. 点击 "New +" -> "Web Service"
4. 连接你的GitHub仓库
5. 配置如下：
   - Name: 你的应用名称
   - Environment: Python 3
   - Build Command: `pip install -r requirements.txt`
   - Start Command: `gunicorn 持仓系统_v14:app --bind 0.0.0.0:$PORT`
6. 点击 "Create Web Service"

## 重要文件说明

- `持仓系统_v14.py` - 主应用文件
- `requirements.txt` - Python依赖包列表
- `Procfile` - 告诉云平台如何启动应用
- `runtime.txt` - 指定Python版本
- `main.py` - 云部署启动文件
- `.gitignore` - Git忽略文件列表

## 注意事项

1. **数据持久化**：云平台重启时会丢失本地文件，建议使用云数据库
2. **环境变量**：敏感信息（如API密钥）应设置为环境变量
3. **定时任务**：某些云平台可能不支持后台定时任务
4. **内存限制**：免费套餐通常有内存限制，注意优化代码

## 故障排除

如果部署失败，检查：
1. requirements.txt 中的包版本是否兼容
2. 代码中是否有硬编码的文件路径
3. 是否使用了不支持的系统功能
4. 查看部署日志获取详细错误信息

## 访问你的应用

部署成功后，你会得到一个类似这样的网址：
- Railway: `https://你的应用名-production.up.railway.app`
- Render: `https://你的应用名.onrender.com`

把这个网址分享给别人，他们就可以访问你的持仓系统了！
