#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署测试脚本
===========

测试系统在云环境中的基本功能。

作者: AI Assistant
版本: 1.0
日期: 2025-07-30
"""

import requests
import json
import time

def test_deployment(base_url):
    """测试部署的应用"""
    print(f"🧪 开始测试部署的应用: {base_url}")
    
    tests = [
        ("主页访问", "/"),
        ("API测试", "/api/stocks"),
        ("健康检查", "/health"),
    ]
    
    results = []
    
    for test_name, endpoint in tests:
        try:
            url = base_url.rstrip('/') + endpoint
            print(f"📡 测试 {test_name}: {url}")
            
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                print(f"✅ {test_name}: 成功")
                results.append((test_name, "成功", response.status_code))
            else:
                print(f"❌ {test_name}: 失败 (状态码: {response.status_code})")
                results.append((test_name, "失败", response.status_code))
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {test_name}: 连接失败 - {e}")
            results.append((test_name, "连接失败", str(e)))
        
        time.sleep(1)  # 避免请求过于频繁
    
    # 输出测试结果
    print("\n📊 测试结果汇总:")
    print("-" * 50)
    for test_name, status, details in results:
        print(f"{test_name}: {status} ({details})")
    
    # 计算成功率
    success_count = sum(1 for _, status, _ in results if status == "成功")
    success_rate = (success_count / len(results)) * 100
    
    print(f"\n🎯 测试成功率: {success_rate:.1f}% ({success_count}/{len(results)})")
    
    if success_rate >= 80:
        print("🎉 部署测试通过！")
        return True
    else:
        print("⚠️ 部署可能存在问题，请检查日志。")
        return False

def main():
    """主函数"""
    print("持仓系统部署测试工具")
    print("=" * 30)
    
    # 获取要测试的URL
    base_url = input("请输入你的应用网址: ").strip()
    
    if not base_url:
        print("❌ 请提供有效的网址")
        return
    
    if not base_url.startswith(('http://', 'https://')):
        base_url = 'https://' + base_url
    
    # 开始测试
    test_deployment(base_url)

if __name__ == '__main__':
    main()
