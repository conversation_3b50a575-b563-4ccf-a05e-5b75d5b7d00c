#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
卖出信号计算模块
================

负责计算股票的卖出信号，包括：
- 多策略信号计算
- 优先级控制
- 手动状态处理
- 信号优先级排序

作者: AI Assistant
版本: 1.0
"""

from typing import Dict, List, Optional
import traceback


# 卖出信号优先级配置
SELL_SIGNAL_PRIORITY_CONFIG = {
    'manual_priority_over_auto': True,  # 手动状态是否优先于自动策略
    'priority_order': {
        'manual_clearance': 0,      # 手动清仓
        'auto_clearance': 1,        # 自动清仓
        'manual_reduce': 2,         # 手动减半
        'auto_reduce': 2,           # 自动减半
        'manual_sell': 3,           # 手动卖出
        'auto_sell': 3,             # 自动卖出
        'auto_warning': 3,          # 自动预警
        'temp_status': 4,           # 临时状态（已挂单、已减半）
        'hold': 5                   # 持有
    }
}


class SellSignalPriorityManager:
    """卖出信号优先级控制管理器"""

    def __init__(self):
        self.config = SELL_SIGNAL_PRIORITY_CONFIG.copy()
        self.load_config()

    def load_config(self):
        """加载优先级配置"""
        try:
            import json
            import os
            config_file = 'config/sell_signal_priority_config.json'
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                    print(f"📂 加载卖出信号优先级配置: 手动优先级{'启用' if self.config['manual_priority_over_auto'] else '禁用'}")
        except Exception as e:
            print(f"❌ 加载优先级配置失败: {e}")

    def save_config(self):
        """保存优先级配置"""
        try:
            import json
            import os
            config_file = 'config/sell_signal_priority_config.json'
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            print(f"💾 保存卖出信号优先级配置")
        except Exception as e:
            print(f"❌ 保存优先级配置失败: {e}")

    def is_manual_priority_enabled(self):
        """检查是否启用手动优先级"""
        return self.config.get('manual_priority_over_auto', True)

    def set_manual_priority(self, enabled: bool):
        """设置手动优先级"""
        self.config['manual_priority_over_auto'] = enabled
        self.save_config()

    def get_config(self):
        """获取配置"""
        return self.config.copy()


def calculate_sell_signal(stock_data: dict, strategy_manager, priority_manager: SellSignalPriorityManager) -> dict:
    """
    多策略卖出信号计算 - 支持优先级控制
    返回: {
        'signal': 'sell'|'warning'|'hold',
        'reason': '卖出原因',
        'color': '显示颜色',
        'priority': 优先级数字,
        'strategies': [触发的策略列表],
        'details': '详细信息',
        'source': 'manual'|'auto'  # 信号来源
    }
    """
    try:
        # 检查是否有手动标记的状态，且优先级控制启用
        if priority_manager.is_manual_priority_enabled():
            # 检查各种手动状态标记
            if stock_data.get('custom_status', False):
                custom_status_text = stock_data.get('custom_status_text', '自定义状态')
                print(f"🔒 检测到自定义状态: {custom_status_text}，手动优先级启用，返回手动状态")

                # 特殊处理"已挂单"和"已减半"状态的排序优先级
                if custom_status_text in ['已挂单', '已减半']:
                    priority = 4  # 临时状态排在策略信号下面，持有上面
                    print(f"📋 临时状态({custom_status_text})，设置特殊优先级: {priority}")
                else:
                    priority = 0  # 其他自定义状态保持最高优先级

                return {
                    'signal': stock_data.get('custom_status_type', 'hold'),
                    'reason': custom_status_text,
                    'color': '#6c757d',  # 灰色表示手动设置
                    'priority': priority,
                    'strategies': [],
                    'details': f"手动设置于 {stock_data.get('custom_status_time', '未知时间')}",
                    'source': 'manual'
                }

            if stock_data.get('is_reduced', False):
                print(f"🔒 检测到已减半状态，设置为临时优先级")
                return {
                    'signal': 'sell',
                    'reason': '已减半',
                    'color': '#6c757d',  # 灰色表示已处理
                    'priority': 4,  # 临时优先级：排在策略信号下面，持有上面
                    'strategies': [],
                    'details': f"已于 {stock_data.get('reduced_time', '未知时间')} 标记为减半",
                    'source': 'manual'
                }

            if stock_data.get('is_cleared', False):
                print(f"🔒 检测到已清仓状态，手动优先级启用，返回手动状态")
                return {
                    'signal': 'sell',
                    'reason': '已清仓',
                    'color': '#dc3545',  # 红色表示已清仓
                    'priority': 0,  # 手动状态最高优先级
                    'strategies': [],
                    'details': f"已于 {stock_data.get('cleared_time', '未知时间')} 标记为清仓",
                    'source': 'manual'
                }

        # 如果没有手动状态或手动优先级未启用，进行自动策略计算
        triggered_strategies = []

        print(f"🔍 开始评估股票 {stock_data.get('name', 'Unknown')} 的自动卖出信号...")

        # 评估所有启用的策略
        for strategy_id, strategy_config in strategy_manager.strategies.items():
            if strategy_config.get('enabled', False):
                print(f"  📊 评估策略: {strategy_config['name']} (enabled: {strategy_config['enabled']})")
                result = strategy_manager.evaluate_strategy(strategy_id, stock_data)
                if result:
                    print(f"    ✅ 策略触发: {result['reason']}")
                    triggered_strategies.append(result)
                else:
                    print(f"    ❌ 策略未触发")
            else:
                print(f"  ⏸️ 跳过禁用策略: {strategy_config['name']}")

        print(f"📋 触发的自动策略数量: {len(triggered_strategies)}")

        if not triggered_strategies:
            # 无触发策略时返回持有信号
            return {
                'signal': 'hold',
                'reason': '继续持有',
                'color': '#2ed573',  # 绿色
                'priority': 5,  # 持有状态最低优先级
                'strategies': [],
                'details': '无触发策略',
                'source': 'auto'
            }

        # 按信号类型和评分排序 - 支持新的信号类型
        clearance_strategies = [s for s in triggered_strategies if s['signal'] == 'clearance']
        reduce_strategies = [s for s in triggered_strategies if s['signal'] == 'reduce']
        sell_strategies = [s for s in triggered_strategies if s['signal'] == 'sell']
        warning_strategies = [s for s in triggered_strategies if s['signal'] == 'warning']

        # 优先级：清仓 > 减半 > 卖出 > 预警
        if clearance_strategies:
            # 有清仓信号，选择评分最高的
            best_strategy = max(clearance_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in clearance_strategies]

            return {
                'signal': 'clearance',
                'reason': ' | '.join(reasons),
                'color': '#8b0000',  # 深红色表示清仓
                'priority': 1,  # 清仓最高优先级
                'strategies': clearance_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

        elif reduce_strategies:
            # 有减半信号，选择评分最高的
            best_strategy = max(reduce_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in reduce_strategies]

            return {
                'signal': 'reduce',
                'reason': ' | '.join(reasons),
                'color': '#ff6b35',  # 橙红色表示减半
                'priority': 2,  # 减半次高优先级
                'strategies': reduce_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

        elif sell_strategies:
            # 有卖出信号，选择评分最高的
            best_strategy = max(sell_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in sell_strategies]

            return {
                'signal': 'sell',
                'reason': ' | '.join(reasons),
                'color': '#ff4757',  # 红色表示卖出
                'priority': 3,  # 卖出第三优先级
                'strategies': sell_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

        elif warning_strategies:
            # 只有预警信号
            best_strategy = max(warning_strategies, key=lambda x: x['score'])
            reasons = [s['reason'] for s in warning_strategies]

            return {
                'signal': 'warning',
                'reason': ' | '.join(reasons),
                'color': '#ffa502',  # 橙色
                'priority': 3,  # 自动预警优先级
                'strategies': warning_strategies,
                'details': f"主要策略: {best_strategy.get('strategy', '未知策略')}",
                'source': 'auto'
            }

    except Exception as e:
        print(f"❌ 计算卖出信号失败: {e}")
        traceback.print_exc()
        return {
            'signal': 'hold',
            'reason': '数据异常',
            'color': '#747d8c',  # 灰色
            'priority': 4,
            'strategies': [],
            'details': f'计算错误: {str(e)}',
            'source': 'auto'
        }


def recalculate_all_sell_signals(stock_data: dict, strategy_manager, priority_manager: SellSignalPriorityManager):
    """重新计算所有股票的卖出信号"""
    try:
        updated_count = 0
        for stock_code, stock_info in stock_data.items():
            # 重新计算卖出信号
            sell_signal = calculate_sell_signal(stock_info, strategy_manager, priority_manager)
            
            # 更新股票信息中的卖出信号
            stock_info['sell_signal'] = sell_signal['signal']
            stock_info['sell_reason'] = sell_signal['reason']
            stock_info['sell_color'] = sell_signal['color']
            stock_info['sell_priority'] = sell_signal['priority']
            stock_info['sell_details'] = sell_signal['details']
            stock_info['sell_source'] = sell_signal['source']
            
            updated_count += 1
        
        print(f"✅ 重新计算完成，更新了 {updated_count} 只股票的卖出信号")
        
        # 保存更新后的数据（需要在调用处实现）
        return True

    except Exception as e:
        print(f"❌ 重新计算卖出信号失败: {e}")
        return False
