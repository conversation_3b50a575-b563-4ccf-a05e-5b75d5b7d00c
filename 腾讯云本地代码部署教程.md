# 🚀 腾讯云CloudBase本地代码部署教程

## 🎯 什么是本地代码部署？

腾讯云CloudBase提供了**两种本地部署方式**：

### 方式1：控制台上传代码包
- 📁 直接上传你的项目文件夹
- 🖱️ 通过网页界面操作
- ⚡ 简单快速，适合新手

### 方式2：CLI命令行部署
- 💻 使用命令行工具
- 🔄 支持自动化部署
- 🛠️ 适合开发者

**重要**：这两种方式都是**云部署**，代码会上传到腾讯云服务器运行！

## 📋 准备工作

### 1. 注册腾讯云账号
- 访问：https://cloud.tencent.com
- 注册并完成实名认证

### 2. 开通CloudBase环境
- 搜索"CloudBase"或"云开发"
- 创建环境（选择按量计费，有免费额度）
- 记住你的**环境ID**

### 3. 准备你的持仓系统代码
确保以下文件存在：
- ✅ `持仓系统_v14.py`
- ✅ `requirements.txt`
- ✅ `Dockerfile`（需要创建）

## 🔧 方式1：控制台上传部署（推荐新手）

### 第1步：创建Dockerfile

在你的项目根目录创建 `Dockerfile` 文件：

```dockerfile
FROM python:3.11-slim

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制项目文件
COPY . /app

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["python", "持仓系统_v14.py"]
```

### 第2步：修改启动端口

修改 `持仓系统_v14.py` 文件末尾：

```python
if __name__ == '__main__':
    # 腾讯云CloudBase默认使用8080端口
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=False)
```

### 第3步：控制台部署

1. **访问CloudBase控制台**：
   - https://tcb.cloud.tencent.com/dev#/platform-run

2. **创建服务**：
   - 点击"新建服务"
   - 选择"通过本地代码部署"

3. **上传代码**：
   - 部署方式：选择"上传代码包"
   - 代码包类型：选择"文件夹"
   - 选择你的项目文件夹

4. **配置服务**：
   - 服务名称：`stock-system`
   - 端口：`8080`
   - CPU：0.25核
   - 内存：0.5GB

5. **部署**：
   - 点击"创建"
   - 等待部署完成（约3-5分钟）

## 💻 方式2：CLI命令行部署（推荐开发者）

### 第1步：安装Node.js
- 访问：https://nodejs.org
- 下载并安装LTS版本

### 第2步：安装CloudBase CLI
```bash
npm install -g @cloudbase/cli
```

### 第3步：登录CloudBase
```bash
# 登录
tcb login

# 查看环境列表
tcb env:list

# 切换到你的环境
tcb env:switch
```

### 第4步：部署应用
```bash
# 在你的项目目录执行
tcb cloudrun deploy -p 8080
```

系统会提示你：
- 选择环境
- 输入服务名称
- 确认部署配置

## 🎉 部署成功后

### 获取访问地址
部署完成后，你会得到一个访问地址：
- 格式：`https://你的服务名-xxx.ap-shanghai.run.tcloudbase.com`

### 测试访问
- 在浏览器中打开这个地址
- 应该能看到你的持仓系统

## 💰 费用说明

### 免费额度（每月）
- **CPU时间**：180,000 核秒
- **内存时间**：360,000 MB秒
- **流量**：1GB
- **构建时长**：100分钟

**个人使用完全免费！**

## 🔧 常见问题

### 1. 部署失败
**检查项目**：
- Dockerfile语法是否正确
- requirements.txt是否包含所有依赖
- 端口配置是否为8080

### 2. 访问超时
**可能原因**：
- 应用启动时间过长
- 内存不足导致重启

**解决方案**：
- 增加CPU和内存配置
- 优化代码启动速度

### 3. 依赖安装失败
**解决方案**：
- 使用国内镜像源
- 检查依赖包版本兼容性

## 🛡️ 安全建议

### 1. 环境变量
在CloudBase控制台设置环境变量：
- `WECHAT_WEBHOOK_URL`：企业微信密钥
- `SECRET_KEY`：应用密钥

### 2. 访问控制
- 设置自定义域名
- 配置访问白名单
- 启用HTTPS

## 🔄 更新部署

### 控制台更新
1. 修改本地代码
2. 重新上传代码包
3. 等待重新部署

### CLI更新
```bash
# 重新部署
tcb cloudrun deploy -p 8080
```

## 📊 监控和日志

### 查看日志
- 在CloudBase控制台
- 点击"日志"标签
- 查看应用运行日志

### 监控指标
- CPU使用率
- 内存使用率
- 请求量统计
- 错误率监控

## 🎯 总结

**腾讯云CloudBase本地代码部署的优势：**
- ✅ 中国用户访问速度快
- ✅ 有免费额度
- ✅ 支持自动扩缩容
- ✅ 提供完整的监控和日志
- ⚠️ 代码会上传到云端

**适合场景：**
- 想要稳定的云服务
- 不介意代码上传到云端
- 需要24小时运行
- 希望有专业的运维支持

**开始部署吧！** 🚀
